// import Image from "next/image"

// type TeamMember = {
//     name: string
//     role: string
//     image: string
// }

export default function TeamSection() {
    // const teamMembers: TeamMember[] = [
    //     {
    //         name: "<PERSON>",
    //         role: "Generative AI Architect",
    //         image: "/Anton-avatar.svg",
    //     },
    //     {
    //         name: "<PERSON>",
    //         role: "Cloud Infrastructure & Security Architect",
    //         image: "/David-avatar.svg",
    //     },
    //     {
    //         name: "<PERSON>",
    //         role: "Product & User Experience Architect",
    //         image: "/Rick-avatar.svg",
    //     },
    // ]

    return (
        <section className="w-full py-16 md:py-24 relative">

            <div
                className="absolute opacity-30 blur-[150px]"
                style={{
                    width: "362.36px",
                    height: "252.34px",
                    top: "-10%",
                    left: "40%",
                    background: "#7F20EF",
                    transform: "rotate(-164.56deg)",
                    zIndex: "0",
                }}
            />

            <div className="container px-4 md:px-6 mx-auto max-w-[1200px] relative">
                <h2 className="text-center text-[28px] md:text-5xl font-bold mb-8 md:mb-12">
                    <span className="bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] bg-clip-text text-transparent">
                        Our Vision
                    </span>
                </h2>

                <div className="text-center mb-12 md:mb-16 max-w-3xl mx-auto">
                    <p className="text-[14px] md:text-[18px] leading-relaxed text-white/80 px-4">
                        Our dedicated team of experts combines deep sports knowledge with 
                        cutting-edge AI expertise to deliver accurate predictions.
                    </p>
                </div>

                {/* <div className="grid grid-cols-2 md:grid-cols-3 gap-2.5 md:gap-[10px] max-w-6xl mx-auto mb-10 md:mb-20" style={{justifyItems: "center"}}>
                    {teamMembers.map((member, index) => (
                        <div
                            key={index}
                            className={`flex flex-col items-center p-4 ${
                                index === 2 ? 'col-span-2 md:col-span-1' : ''
                            } md:w-[218px] md:h-[305px] md:rounded-[24px] md:bg-[#00000047] md:backdrop-blur-[20px]`}
                        >
                            <div className="mb-4 rounded-full overflow-hidden border-gradient p-1">
                                <Image
                                    src={member.image}
                                    alt={member.name}
                                    width={150}
                                    height={150}
                                    className="rounded-full w-[120px] h-[120px] md:w-[150px] md:h-[150px] object-cover"
                                />
                            </div>
                            <h3 className="text-[18px] md:text-2xl font-semibold mb-2 text-center">{member.name}</h3>
                            <p className="text-[14px] md:text-base text-muted-foreground text-center">{member.role}</p>
                        </div>
                    ))}
                </div> */}

                <div className="text-center max-w-4xl mx-auto px-4 md:px-0">
                    <p className="text-[18px] md:text-[22px] leading-relaxed text-white">
                        <span className="font-bold">DarkHorseWin.com</span> is built by <span className="font-bold">AI engineers, data scientists,
                            and sports analysts</span> who share a passion for analytics and innovation.
                        <span className="block mt-4 md:mt-6">
                            Our team combines expertise in artificial intelligence, cloud infrastructure, and user experience
                            to deliver a powerful platform that helps you make smarter decisions.
                        </span>
                    </p>
                </div>
            </div>
        </section>
    )
}
