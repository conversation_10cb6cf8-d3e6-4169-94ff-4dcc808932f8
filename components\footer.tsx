import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"

const Footer = (): JSX.Element => {
  return (
    <footer className="w-full py-6 sm:py-8 md:py-10">
      <div className="container mx-auto px-4">
        <div className="hidden lg:flex lg:flex-row lg:items-center lg:justify-between">
          <div className="flex items-center">
            <div className="flex items-center mr-4">
              <div className="flex items-center">
                <Image 
                  src="https://borisbelov.com/dev/darkhorsewin/darkhorselogo.svg" 
                  alt="DarkHorse Icon" 
                  width={24} 
                  height={26} 
                  className="mr-2" 
                />
                <Image 
                  src="/DARKHORSE.svg" 
                  alt="DarkHorse" 
                  width={120} 
                  height={20} 
                  className="w-auto h-auto" 
                />
              </div>
            </div>
            <span className="text-muted-foreground text-sm">DarkHorseWin ©2024 Copyright</span>
          </div>

          <div className="flex items-center space-x-6">
            <Link 
              href="/terms" 
              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              Terms of use
            </Link>
            <Link 
              href="/privacy" 
              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              Privacy policy
            </Link>
          </div>

          <Button 
            className="border border-white/20 bg-transparent text-white text-[16px] sm:text-[14px] font-medium rounded-full w-[224px] h-[48px] flex items-center justify-center hover:bg-white/10 transition-all"
          >
            Get AI Predictions
          </Button>
        </div>

        <div className="flex flex-col items-center lg:hidden">
          <div className="mb-6 sm:mb-8 flex items-center">
            <Image 
              src="https://borisbelov.com/dev/darkhorsewin/darkhorselogo.svg" 
              alt="DarkHorse Icon" 
              width={24} 
              height={26} 
              className="mr-2" 
            />
            <Image 
              src="/DARKHORSE.svg" 
              alt="DarkHorse" 
              width={120} 
              height={20} 
              className="w-auto h-auto" 
            />
          </div>

          <div className="flex flex-row items-center space-x-6 sm:space-x-8 mb-6 sm:mb-8">
            <Link 
              href="/terms" 
              className="text-foreground hover:text-muted-foreground transition-colors"
            >
              Terms of use
            </Link>
            <Link 
              href="/privacy" 
              className="text-foreground hover:text-muted-foreground transition-colors"
            >
              Privacy policy
            </Link>
          </div>

          <Button 
            variant="outline" 
            className="w-[211px] h-[44px] rounded-[40px] border border-white/20 mb-6 sm:mb-8 gap-2 hover:bg-white/10 transition-all duration-200"
          >
            Get AI Predictions
          </Button>

          <span className="text-muted-foreground text-sm">
            DarkHorseWin ©2024 Copyright
          </span>
        </div>
      </div>
    </footer>
  )
}

export default Footer





