export interface NBAGameKey {
    game_id: string;
    week_number: number;
  }
  
  export interface NBABatchPredictionRequest {
    user_id: string;
    keys: NBAGame<PERSON>ey[];
  }
  
  export interface NBAPredictionUploadRequest {
    csv_path: string;
  }
  
  export interface NBATrainModelRequest {
    training_seasons?: number[] | null;
    current_week?: number | null;
  }
  
  export interface NBATrainRlAgentRequest {
    training_seasons?: number[] | null;
    current_week?: number | null;
    num_select?: number | null;
    max_num_games?: number | null;
    timesteps?: number | null;
  }
  
  export interface NBAPredictUpcomingGamesRequest {
    training_seasons?: number[] | null;
    current_week?: number | null;
    prediction_season?: string | null;
  }
  
  export interface NBAPick6Request {
    num_select?: number | null;
    max_num_games?: number | null;
    current_week?: number | null;
  }
  