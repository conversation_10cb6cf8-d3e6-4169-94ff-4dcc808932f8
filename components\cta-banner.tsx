export default function CTABanner() {
  return (
    <section className="w-full py-16 md:py-24 relative overflow-hidden"
      style={{
        background: "#1F16279C",
        borderWidth: "2px 0px 2px 0px",
        borderStyle: "solid",
        borderImageSource: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
        borderImageSlice: "1",
        backdropFilter: "blur(44px)",
        WebkitBackdropFilter: "blur(44px)",
      }}
    >
      <div
        className="absolute opacity-30 blur-[100px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "24.45px",
          left: "46px",
          background: "#7F20EF",
          transform: "rotate(15.44deg)",
          zIndex: "0",
        }}
      />

      <div
        className="absolute opacity-30 blur-[100px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          bottom: "24.45px",
          right: "46px",
          background: "#2CAD9C",
          transform: "rotate(15.44deg)",
          zIndex: "0",
        }}
      />

      <div className="w-full container mx-auto text-center px-4 relative z-10">
        <h2 className="text-3xl md:text-4xl lg:text-[48px] font-bold mb-6 text-white leading-normal md:leading-relaxed">
          Elevate Your Sports Knowledge –<br className="hidden xl:block md:block" /> DarkHorse<br className="xl:hidden md:hidden" /> &nbsp;
          <span
            style={{
              background: "linear-gradient(263.61deg, #19FB9B 25.9%, #8C01FA 79.13%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
            }}
          >
            Your Picks Today!
          </span>
        </h2>

        <p className="text-white text-lg md:text-xl mb-10">
          Real-Time Insights <span className="mx-2">|</span> Proven Accuracy <span className="mx-2">|</span> No
          Bookmaker Bias
        </p>

        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 w-full">
          <button className="bg-transparent border border-white/20 text-white text-[14px] font-medium rounded-full w-full sm:w-[224px] h-[48px] flex items-center justify-center hover:bg-white/10 transition-all">
            Choose a Plan
          </button>
          <button className="bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white text-[14px] font-medium rounded-full w-full sm:w-[224px] h-[48px] flex items-center justify-center hover:opacity-90 transition-opacity">
            See AI Predictions
          </button>
        </div>
      </div>
    </section>
  )
}




