import { API_BASE_URL, API_ENDPOINTS } from './config';
import type { CreateUserRequest, User, ApiResponse } from '@/types/api';

export class UserService {
  static async createUser(userData: CreateUserRequest, token: string): Promise<ApiResponse<{ user_id: string }>> {
    try {
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.users.create}?token=${encodeURIComponent(token)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();

      if (!response.ok) {
        return { error: data };
      }

      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to create user',
        },
      };
    }
  }

  static async getUserByEmail(email: string): Promise<ApiResponse<{ user_id: string }>> {
    try {
      const response = await fetch(
        `${API_BASE_URL}${API_ENDPOINTS.users.getByEmail}?email=${encodeURIComponent(email)}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const data = await response.json();

      if (!response.ok) {
        return { error: data };
      }

      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to get user',
        },
      };
    }
  }

  static async updateUserCredits(
    userId: string, 
    credits: { aiChatCredits?: number; predictionCredits?: number }
  ): Promise<ApiResponse<User>> {
    try {
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.users.update(userId)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credits),
      });

      const data = await response.json();

      if (!response.ok) {
        return { error: data };
      }

      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to update user credits',
        },
      };
    }
  }
}



