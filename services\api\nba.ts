import { API_BASE_URL, API_ENDPOINTS } from './config';
import type { ApiResponse } from '@/types/api';
import type {
  NBAGameKey,
  NBABatchPredictionRequest,
  NBAPredictionUploadRequest,
  NBATrainModelRequest,
  NBATrainRlAgentRequest,
  NBAPredictUpcomingGamesRequest,
  NBAPick6Request
} from '@/types/nba';

export class NBAService {
  static async uploadPredictions(
    csvPath: string,
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nba.predictions.upload}?token=${encodeURIComponent(token)}`;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ csv_path: csvPath } as NBAPredictionUploadRequest),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to upload NBA predictions',
        },
      };
    }
  }

  static async getGamesByWeek(
    weekNumber: number,
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nba.predictions.getByWeek(weekNumber)}?token=${encodeURIComponent(token)}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: `Failed to get NBA games for week ${weekNumber}`,
        },
      };
    }
  }

  static async getPredictionsByGameIds(
    userId: string,
    gameKeys: NBAGameKey[],
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nba.predictions.getByGameIds}?token=${encodeURIComponent(token)}`;

      const requestBody: NBABatchPredictionRequest = {
        user_id: userId,
        keys: gameKeys
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to get NBA predictions for the specified games',
        },
      };
    }
  }

  static async trainModel(
    trainingSeasons: number[] = [2023, 2024],
    currentWeek: number = 10,
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nba.trainModel}?token=${encodeURIComponent(token)}`;

      const requestBody: NBATrainModelRequest = {
        training_seasons: trainingSeasons,
        current_week: currentWeek
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to train NBA model',
        },
      };
    }
  }

  static async trainRlAgent(
    trainingSeasons: number[] = [2023, 2024],
    currentWeek: number = 10,
    numSelect: number = 6,
    maxNumGames: number = 96,
    timesteps: number = 150000,
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nba.trainRlAgent}?token=${encodeURIComponent(token)}`;

      const requestBody: NBATrainRlAgentRequest = {
        training_seasons: trainingSeasons,
        current_week: currentWeek,
        num_select: numSelect,
        max_num_games: maxNumGames,
        timesteps: timesteps
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to train NBA RL agent',
        },
      };
    }
  }

  static async predictUpcomingGames(
    trainingSeasons: number[] = [2023, 2024],
    currentWeek: number = 10,
    predictionSeason: string = "2024",
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nba.predictUpcomingGames}?token=${encodeURIComponent(token)}`;

      const requestBody: NBAPredictUpcomingGamesRequest = {
        training_seasons: trainingSeasons,
        current_week: currentWeek,
        prediction_season: predictionSeason
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to predict upcoming NBA games',
        },
      };
    }
  }

  static async getPick6(
    numSelect: number = 6,
    maxNumGames: number = 96,
    currentWeek: number = 10,
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nba.pick6}?token=${encodeURIComponent(token)}`;

      const requestBody: NBAPick6Request = {
        num_select: numSelect,
        max_num_games: maxNumGames,
        current_week: currentWeek
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to get NBA Pick6 selections',
        },
      };
    }
  }
}