import { fetchAuthSession } from "aws-amplify/auth/server";
import { NextRequest, NextResponse } from "next/server";
import { runWithAmplifyServerContext } from "@/app/utils/amplifyServerUtils";

const PUBLIC_ROUTES = ['/', '/privacy-policy', '/terms-of-service'];
const AUTH_ROUTES = ['/login', '/signup', '/verify-email', '/forgot-password'];
const PROTECTED_ROUTES = ['/main', '/game', '/profile', '/settings'];

export async function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl;
    const response = NextResponse.next();

    if (
        pathname.startsWith('/_next') ||
        pathname.startsWith('/api') ||
        pathname.includes('.') ||
        pathname.startsWith('/static')
    ) {
        return NextResponse.next();
    }

    if (PUBLIC_ROUTES.includes(pathname)) {
        return NextResponse.next();
    }

    const authenticated = await runWithAmplifyServerContext({
        nextServerContext: { request, response },
        operation: async (contextSpec) => {
            try {
                const session = await fetchAuthSession(contextSpec);
                return session.tokens !== undefined;
            } catch (error) {
                console.error('Auth check error:', error);
                return false;
            }
        },
    });


    if (authenticated) {
        if (PROTECTED_ROUTES.some(route => pathname.startsWith(route))) {
            return response;
        }
        if (AUTH_ROUTES.some(route => pathname.startsWith(route))) {
            return NextResponse.redirect(new URL("/main", request.url));
        }
    } else {
        if (PROTECTED_ROUTES.some(route => pathname.startsWith(route))) {
            return NextResponse.redirect(new URL("/login", request.url));
        }
        if (AUTH_ROUTES.some(route => pathname.startsWith(route))) {
            return NextResponse.next();
        }
    }

    return NextResponse.next();
}

export const config = {
    matcher: [
        '/((?!api|_next|static|[\\w-]+\\.\\w+).*)',
    ],
};
