"use client"

import { useState } from 'react'
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ProfileMenu } from "@/components/profile-menu"
import { NotificationsPanel } from "@/components/notifications-panel"
import { SettingsPanel } from "@/components/settings-panel"
import { HelpInfoPanel } from "@/components/help-info-panel"
import { ContactPanel } from "@/components/contact-panel"
import { SignOutPanel } from "@/components/sign-out-panel"
import BettingInterface from "@/components/betting-interface"
import { usePathname } from 'next/navigation'
import { cn } from "@/lib/utils"

interface RightAsideContentProps {
  currentView: 'slip' | 'profile' | 'notifications' | 'settings' | 'help-info' | 'contact' | 'sign-out';
  onViewChange: (view: 'slip' | 'profile' | 'notifications' | 'settings' | 'help-info' | 'contact' | 'sign-out') => void;
}

export default function RightAsideContent({ currentView, onViewChange }: RightAsideContentProps) {
  const pathname = usePathname()
  const isGamePage = pathname.startsWith('/game/')

  const handleNotificationsClick = () => onViewChange('notifications');
  const handleSettingsClick = () => onViewChange('settings');
  const handleHelpInfoClick = () => onViewChange('help-info');
  const handleContactClick = () => onViewChange('contact');
  const handleSignOutClick = () => onViewChange('sign-out');
  const handleBackToProfile = () => onViewChange('profile');

  const EmptySlipContent = () => (
    <div>
      <div className="flex items-center justify-between border-b border-[#2D3643] border-solid pb-4">
        <div className="flex items-center gap-2">
          <div className="relative">
            <div className="relative">
              <Image
                src="/slip-icon.svg"
                alt="Slip Icon"
                width={26}
                height={26}
              />
              <span className="absolute top-[12px] left-1/2 -translate-x-1/2 -translate-y-1/2 text-white text-xl font-bold">0</span>
            </div>
          </div>
          <h2 className="text-lg font-medium">Prediction Slip</h2>
        </div>
      </div>

      <div className="mt-8 flex flex-col items-center justify-center text-center">
        <div className="mb-4 flex items-center justify-center">
          <Image
            src="/empty.svg"
            alt="Empty Icon"
            width={43}
            height={53}
          />
        </div>
        <h3 className="mb-2 text-lg font-medium">Prediction Slip is empty</h3>
        <p className="mb-6 text-sm text-[#9ca2b5]">
          To add a bet to your Prediction Slip,
          <br />
          choose a market and make your selection
        </p>
        <Button
          variant="outline"
          className="border-[#2D3643] border rounded-[88px] border-solid text-white hover:bg-[#28303f] hover:text-white px-8 py-2"
        >
          How it works?
        </Button>
      </div>
    </div>
  )

  const GameSlipContent = () => {
    const [betType, setBetType] = useState<'single' | 'parlay'>('single')

    return (
      <div>
        <div className="flex items-center justify-between border-b border-[#2D3643] border-solid pb-4">
          <div className="flex items-center gap-2">
            <div className="relative">
              <div className="relative">
                <Image
                  src="/slip-icon.svg"
                  alt="Slip Icon"
                  width={26}
                  height={26}
                />
                <span className="absolute top-[12px] left-1/2 -translate-x-1/2 -translate-y-1/2 text-white text-xl font-bold">4</span>
              </div>
            </div>
            <h2 className="text-lg font-medium">Prediction Slip</h2>
          </div>
          <p
            className="text-xs text-white hover:bg-[#28303f] hover:text-white px-4 py-1 rounded-full cursor-pointer"
          >
            Clear All
          </p>
        </div>

        <div className="w-full grid grid-cols-2 gap-0 my-4 sm:my-6">
          <button
            onClick={() => setBetType('single')}
            className={cn(
              "py-2 sm:py-3 rounded-l-[8px] font-medium transition-all text-sm sm:text-base relative",
              betType === 'single'
                ? "text-white"
                : "bg-[#181c23] text-[#bdbac0] hover:bg-[#313038]"
            )}
          >
            {betType === 'single' && (
              <>
                <div
                  className="absolute inset-0 w-full h-full rounded-l-[8px]"
                  style={{
                    background: "linear-gradient(0deg, rgba(11, 10, 10, 0.4), rgba(0, 0, 0, 0.4)), linear-gradient(261.81deg, rgba(25, 251, 155, 0.4) -8.01%, rgba(140, 1, 250, 0.4) 100%)"
                  }}
                />
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 0,
                    borderRadius: "8px",
                    padding: "1.5px",
                    background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                    WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                    WebkitMaskComposite: "xor",
                    maskComposite: "exclude",
                    pointerEvents: "none",
                  }}
                />
              </>
            )}
            <span className="relative z-10">Singles</span>
          </button>
          <button
            onClick={() => setBetType('parlay')}
            className={cn(
              "py-2 sm:py-3 rounded-r-[8px] font-medium transition-all text-sm sm:text-base relative",
              betType === 'parlay'
                ? "text-white"
                : "bg-[#181c23] text-[#bdbac0] hover:bg-[#313038]"
            )}
          >
            {betType === 'parlay' && (
              <>
                <div
                  className="absolute inset-0 w-full h-full rounded-r-[8px]"
                  style={{
                    background: "linear-gradient(0deg, rgba(11, 10, 10, 0.4), rgba(0, 0, 0, 0.4)), linear-gradient(261.81deg, rgba(25, 251, 155, 0.4) -8.01%, rgba(140, 1, 250, 0.4) 100%)"
                  }}
                />
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 0,
                    borderRadius: "8px",
                    padding: "1.5px",
                    background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                    WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                    WebkitMaskComposite: "xor",
                    maskComposite: "exclude",
                    pointerEvents: "none",
                  }}
                />
              </>
            )}
            <span className="relative z-10">Parlay</span>
          </button>
        </div>

        <div className="mt-4">
          {betType === 'single' ? (
            <BettingInterface />
          ) : (
            <div className="flex flex-col items-center justify-center text-center py-8">
              <h3 className="mb-2 text-lg font-medium">Parlay bets coming soon</h3>
              <p className="text-sm text-[#9ca2b5]">
                This feature is currently under development
              </p>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <>
      {currentView === 'slip' ? (
        isGamePage ? <GameSlipContent /> : <EmptySlipContent />
      ) : currentView === 'profile' ? (
        <ProfileMenu
          name="Boris Belov"
          email="<EMAIL>"
          onNotificationsClick={handleNotificationsClick}
          onSettingsClick={handleSettingsClick}
          onHelpInfoClick={handleHelpInfoClick}
          onContactClick={handleContactClick}
          onSignOutClick={handleSignOutClick}
        />
      ) : currentView === 'notifications' ? (
        <NotificationsPanel onBack={handleBackToProfile} />
      ) : currentView === 'settings' ? (
        <SettingsPanel onBack={handleBackToProfile} />
      ) : currentView === 'help-info' ? (
        <HelpInfoPanel onBack={handleBackToProfile} />
      ) : currentView === 'contact' ? (
        <ContactPanel onBack={handleBackToProfile} />
      ) : currentView === 'sign-out' ? (
        <SignOutPanel onBack={handleBackToProfile} />
      ) : null}
    </>
  );
}
