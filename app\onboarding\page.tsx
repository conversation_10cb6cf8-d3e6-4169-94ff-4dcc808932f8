"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Inter } from "next/font/google"
import NFLIcon from "@/components/svg/nfl-team-icon"
import NBAIcon from "@/components/svg/nba-team-icon"
import MLBIcon from "@/components/svg/mlb-team-icon"
import MLSIcon from "@/components/svg/mls-team-icon"
import { useIsMobile } from "@/hooks/use-mobile"

const inter = Inter({ subsets: ["latin"] })

export default function OnboardingPage() {
  const router = useRouter()
  const [selectedSport, setSelectedSport] = useState<string | null>(null)
  const [isMobileView, setIsMobileView] = useState(true)
  
  // Check if we're in mobile view
  useEffect(() => {
    const checkMobile = () => {
      setIsMobileView(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const selectSport = (sport: string) => {
    setSelectedSport(sport === selectedSport ? null : sport)
  }

  const handleNext = () => {
    if (selectedSport) {
      localStorage.setItem("sportPreference", selectedSport)
    }
    router.push("/main")
  }

  const handleSkip = () => {
    router.push("/main")
  }

  return (
    <div className="flex min-h-screen md:max-h-screen bg-[#050816] p-4 md:p-[20px] gap-[40px] relative overflow-hidden">
      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "10px",
          left: "0%",
          background: "#2CAD9C",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />
      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "450px",
          right: "0%",
          background: "#7F20EF",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />

      <div className="w-full flex items-center justify-center relative z-10">
        <div className="w-full max-w-[540px] md:max-w-[800px] flex flex-col items-center">
          <div className="md:hidden">
            <Link href="/" className="flex items-center">
              <div
                className="flex items-center rounded-[100px] w-[188px] h-[40px] px-6 relative mb-12"
                style={{
                  gap: "5px",
                  background:
                    "linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)",
                }}
              >
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 0,
                    borderRadius: "100px",
                    padding: "1.5px",
                    background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                    WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                    WebkitMaskComposite: "xor",
                    maskComposite: "exclude",
                    pointerEvents: "none",
                  }}
                />
                <Image src="https://borisbelov.com/dev/darkhorsewin/darkhorselogo.svg" alt="DarkHorse Icon" width={24} height={26} />
                <Image src="/DARKHORSE.svg" alt="DarkHorse" width={120} height={20} />
              </div>
            </Link>
          </div>

          <div className="space-y-6 md:space-y-8 w-full">
            <div className="text-center">
              <h1 className="text-[20px] md:text-[28px] font-bold text-white mb-2">Select your sports and betting<br />preferences for personalized updates</h1>
            </div>

            <div className={`${isMobileView ? 'grid grid-cols-2 gap-8 px-3' : 'flex flex-wrap justify-between'} w-full`}>
              <div 
                className={`p-4 md:p-6 ${isMobileView ? '' : 'mx-2 mb-4'} rounded-[22.5px] flex flex-col items-center justify-center cursor-pointer transition-all duration-300 min-w-[150px] md:min-w-[150px] relative ${
                  selectedSport === 'NFL' 
                    ? 'bg-gradient-to-br from-[#19FB9B]/20 to-[#8C01FA]/20' 
                    : 'bg-[#3131314D] backdrop-blur-[20px] border border-[#282828]'
                } outline-none focus:outline-none`}
                onClick={() => selectSport('NFL')}
              >
                {selectedSport === 'NFL' && (
                  <div
                    style={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      bottom: 0,
                      left: 0,
                      borderRadius: '22.5px',
                      padding: '1.5px',
                      background: 'linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)',
                      WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                      WebkitMaskComposite: 'xor',
                      maskComposite: 'exclude',
                      pointerEvents: 'none',
                    }}
                  />
                )}
                <NFLIcon 
                  teamName={`nfl${selectedSport === 'NFL' ? '' : '-grey'}`} 
                  className="w-16 h-16 md:w-18 md:h-18 mb-1 md:mb-2"
                />
                <span className="font-medium text-sm md:text-base text-white">NFL</span>
              </div>

              <div 
                className={`p-4 md:p-6 ${isMobileView ? '' : 'mx-2 mb-4'} rounded-[22.5px] flex flex-col items-center justify-center cursor-pointer transition-all duration-300 min-w-[150px] md:min-w-[150px] relative ${
                  selectedSport === 'NBA' 
                    ? 'bg-gradient-to-br from-[#19FB9B]/20 to-[#8C01FA]/20' 
                    : 'bg-[#3131314D] backdrop-blur-[20px] border border-[#282828]'
                } outline-none focus:outline-none`}
                onClick={() => selectSport('NBA')}
              >
                {selectedSport === 'NBA' && (
                  <div
                    style={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      bottom: 0,
                      left: 0,
                      borderRadius: '22.5px',
                      padding: '1.5px',
                      background: 'linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)',
                      WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                      WebkitMaskComposite: 'xor',
                      maskComposite: 'exclude',
                      pointerEvents: 'none',
                    }}
                  />
                )}
                <NBAIcon 
                  teamName={`nba${selectedSport === 'NBA' ? '' : '-grey'}`} 
                  className="w-16 h-16 md:w-18 md:h-18 mb-1 md:mb-2"
                />
                <span className="font-medium text-sm md:text-base text-white">NBA</span>
              </div>

              <div 
                className={`p-4 md:p-6 ${isMobileView ? '' : 'mx-2 mb-4'} rounded-[22.5px] flex flex-col items-center justify-center cursor-pointer transition-all duration-300 min-w-[150px] md:min-w-[150px] relative ${
                  selectedSport === 'MLB' 
                    ? 'bg-gradient-to-br from-[#19FB9B]/20 to-[#8C01FA]/20' 
                    : 'bg-[#3131314D] backdrop-blur-[20px] border border-[#282828]'
                } outline-none focus:outline-none`}
                onClick={() => selectSport('MLB')}
              >
                {selectedSport === 'MLB' && (
                  <div
                    style={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      bottom: 0,
                      left: 0,
                      borderRadius: '22.5px',
                      padding: '1.5px',
                      background: 'linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)',
                      WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                      WebkitMaskComposite: 'xor',
                      maskComposite: 'exclude',
                      pointerEvents: 'none',
                    }}
                  />
                )}
                <MLBIcon 
                  teamName={`mlb${selectedSport === 'MLB' ? '' : '-grey'}`} 
                  className="w-16 h-16 md:w-18 md:h-18 mb-1 md:mb-2"
                />
                <span className="font-medium text-sm md:text-base text-white">MLB</span>
              </div>

              <div 
                className={`p-4 md:p-6 ${isMobileView ? '' : 'mx-2 mb-4'} rounded-[22.5px] flex flex-col items-center justify-center cursor-pointer transition-all duration-300 min-w-[150px] md:min-w-[150px] relative ${
                  selectedSport === 'MLS' 
                    ? 'bg-gradient-to-br from-[#19FB9B]/20 to-[#8C01FA]/20' 
                    : 'bg-[#3131314D] backdrop-blur-[20px] border border-[#282828]'
                } outline-none focus:outline-none`}
                onClick={() => selectSport('MLS')}
              >
                {selectedSport === 'MLS' && (
                  <div
                    style={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      bottom: 0,
                      left: 0,
                      borderRadius: '22.5px',
                      padding: '1.5px',
                      background: 'linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)',
                      WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                      WebkitMaskComposite: 'xor',
                      maskComposite: 'exclude',
                      pointerEvents: 'none',
                    }}
                  />
                )}
                <MLSIcon 
                  teamName={`mls${selectedSport === 'MLS' ? '' : '-grey'}`} 
                  className="w-16 h-16 md:w-18 md:h-18 mb-1 md:mb-2"
                />
                <span className="font-medium text-sm md:text-base text-white">MLS</span>
              </div>
            </div>

            <div className="flex flex-col items-center gap-3 md:gap-4 pt-4 md:pt-6">
              {isMobileView && (
                <button 
                  className="text-white text-xs hover:text-white transition-colors mb-2 underline"
                  onClick={handleSkip}
                >
                  Skip
                </button>
              )}
              
              <Button 
                className={`${isMobileView ? 'w-[90%]' : 'w-[140px]'} h-[45px] md:h-[50px] rounded-[88px] gap-[8px] px-[8px] py-[8px] bg-gradient-to-r from-[#19FB9B] to-[#8C01FA] text-white hover:opacity-90 text-sm md:text-base`}
                onClick={handleNext}
              >
                Next
              </Button>
              
              {!isMobileView && (
                <button 
                  className="text-white text-sm hover:text-white transition-colors mt-4"
                  onClick={handleSkip}
                >
                  Skip
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}


