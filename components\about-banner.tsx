import SearchStatusIcon from "./svg/search-status-icon"
import CloudAddIcon from "./svg/cloud-add-icon"
import HierarchyIcon from "./svg/hierarchy-icon"
import DiagramIcon from "./svg/diagram-icon"

export default function AboutBanner() {
    return (
        <section className="w-full py-16 md:py-24 relative overflow-hidden"
            style={{
                background: "#1F16279C",
                borderWidth: "2px 0px 2px 0px",
                borderStyle: "solid",
                borderImageSource: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                borderImageSlice: "1",
                backdropFilter: "blur(44px)",
                WebkitBackdropFilter: "blur(44px)",
            }}
        >
            <div
                className="absolute opacity-30 blur-[100px]"
                style={{
                    width: "362.36px",
                    height: "252.34px",
                    top: "24.45px",
                    left: "46px",
                    background: "#7F20EF",
                    transform: "rotate(15.44deg)",
                    zIndex: "0",
                }}
            />

            <div
                className="absolute opacity-30 blur-[100px]"
                style={{
                    width: "362.36px",
                    height: "252.34px",
                    bottom: "24.45px",
                    right: "46px",
                    background: "#2CAD9C",
                    transform: "rotate(15.44deg)",
                    zIndex: "0",
                }}
            />

            <div className="w-full container mx-auto text-center px-4 relative z-10">
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-white">
                    Why Choose DarkHorseWin.com?
                </h2>

                <p className="text-white text-lg md:text-xl mb-10">
                    Explore DarkHorseWin.com today and ride our analytics to the finish line!
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-[10px] max-w-[894px] mx-auto mb-4 md:mb-8">
                    <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] p-4 md:p-6 text-left mx-auto">
                        <SearchStatusIcon className="h-10 w-10 mb-4" />
                        <h3 className="text-white text-[18px] md:text-[20px] font-bold mb-2 md:mb-3">AI-Driven Accuracy</h3>
                        <p className="text-white/80 text-[14px] md:text-[16px]">Predictive models built for <span className="font-bold">transparency & reliability.</span></p>
                    </div>

                    <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] p-4 md:p-6 text-left mx-auto">
                        <DiagramIcon className="h-10 w-10 mb-4" />
                        <h3 className="text-white text-[18px] md:text-[20px] font-bold mb-2 md:mb-3">Independent Insights</h3>
                        <p className="text-white/80 text-[14px] md:text-[16px]">
                            No bookmaker bias—just pure <span className="font-bold">data analysis.</span>
                        </p>
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-[10px] max-w-[894px] mx-auto">
                    <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] p-4 md:p-6 text-left mx-auto">
                        <CloudAddIcon className="h-10 w-10 mb-4" />
                        <h3 className="text-white text-[18px] md:text-[20px] font-bold mb-2 md:mb-3">Continuous Learning</h3>
                        <p className="text-white/80 text-[14px] md:text-[16px] mb-4 md:mb-6">Our AI evolves with every game, <span className="font-bold">making it smarter over time.</span>
                        </p>
                    </div>

                    <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] p-4 md:p-6 text-left mx-auto">
                        <HierarchyIcon className="h-10 w-10 mb-4" />
                        <h3 className="text-white text-[18px] md:text-[20px] font-bold mb-2 md:mb-3">User-Friendly Support</h3>
                        <p className="text-white/80 text-[14px] md:text-[16px] mb-4 md:mb-6">Whether you need guidance or have feedback, <span className="font-bold">our support team is ready to help.</span>
                        </p>
                    </div>
                </div>
            </div>
        </section>
    )
}






