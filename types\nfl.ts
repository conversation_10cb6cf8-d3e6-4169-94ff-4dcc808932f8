export interface NFLGameKey {
  game_id: string;
  week_number: number;
}

export interface NFLBatchPredictionRequest {
  user_id: string;
  keys: NFLGameKey[];
}

export interface NFLPredictionUploadRequest {
  csv_path: string;
}

export interface NFLTrainModelRequest {
  training_seasons?: number[] | null;
  current_week?: number | null;
}

export interface NFLTrainRlAgentRequest {
  training_seasons?: number[] | null;
  current_week?: number | null;
  num_select?: number | null;
  max_num_games?: number | null;
  timesteps?: number | null;
}

export interface NFLPredictUpcomingGamesRequest {
  training_seasons?: number[] | null;
  current_week?: number | null;
  prediction_season?: string | null;
}

export interface NFLPick6Request {
  num_select?: number | null;
  max_num_games?: number | null;
  current_week?: number | null;
}
