# DarkHorse - AI-Powered Sports Predictions Platform

![DarkHorse Logo](public/horse_icon.svg)

[![deploy-frontend](https://github.com/DarkHorse-2PG/frontend-app/actions/workflows/deploy.yaml/badge.svg)](https://github.com/DarkHorse-2PG/frontend-app/actions/workflows/deploy.yaml)

## Overview

DarkHorse is a cutting-edge web application that provides AI-powered sports predictions and analytics. Built with Next.js 15 and TypeScript, it offers real-time insights and predictions for sports enthusiasts.

## Key Features

- **Real-Time Pre-Game Predictions**: Updated automatically as new team statistics become available
- **AI-Powered Analysis**: Advanced machine learning algorithms for accurate predictions
- **Interactive UI Components**: Built with Radix UI and shadcn/ui
- **Responsive Design**: Fully optimized for all device sizes
- **Dark Mode Support**: Built-in theme support using next-themes
- **Interactive Carousels**: Powered by Embla Carousel
- **Form Handling**: Integrated with React Hook Form and Zod validation
- **Modern Animations**: Using Tailwind CSS animations

## 🛠 Tech Stack

- **Framework**: Next.js 15.1.0
- **Language**: TypeScript 5
- **Styling**: 
  - Tailwind CSS 3.4
  - CSS Variables
  - Class Variance Authority
- **UI Components**:
  - Radix UI Primitives
  - shadcn/ui Components
- **Icons**: Lucide React
- **Form Management**: React Hook Form with Zod
- **Data Visualization**: Recharts
- **Carousel**: Embla Carousel
- **Toast Notifications**: Sonner
- **Date Handling**: date-fns

## 📦 Installation

1. Clone the repository:
```bash
git clone https://github.com/DarkHorse-2PG/frontend-app.git
```

2. Install dependencies:
```bash
pnpm install
```

3. Create a `.env` file based on `.env.example`:
```bash
cp .env.example .env
```

4. Start the development server:
```bash
pnpm dev
```

## 🏗 Project Structure

```
darkhorse/
├── app/                   # Next.js app directory
│   ├── layout.tsx        # Root layout
│   └── page.tsx          # Home page
├── components/           # React components
│   ├── ui/              # Reusable UI components
│   └── [feature]/       # Feature-specific components
├── lib/                 # Utility functions
├── hooks/               # Custom React hooks
├── public/              # Static assets
└── styles/              # Global styles
```

## 🔧 Configuration Files

- `components.json`: shadcn/ui configuration
- `tailwind.config.ts`: Tailwind CSS configuration
- `next.config.mjs`: Next.js configuration
- `tsconfig.json`: TypeScript configuration

## 🎨 UI Components

The project uses a comprehensive set of UI components from Radix UI:
- Accordion
- Alert Dialog
- Avatar
- Carousel
- Dialog
- Dropdown Menu
- Navigation Menu
- And many more...

## 🚀 Development

```bash
# Start development server
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start

# Run linting
pnpm lint
```

## 🔐 Environment Variables

Create a `.env` file with the following variables:
```env
NEXT_PUBLIC_API_URL=your_api_url
NEXT_PUBLIC_ASSET_PREFIX=your_asset_prefix
NEXT_PUBLIC_COGNITO_USER_POOL_ID=your_cognito_user_pool_id
NEXT_PUBLIC_COGNITO_CLIENT_ID=your_cognito_client_id
```

These variables are required for:
- API connectivity
- Asset serving
- AWS Cognito authentication

## 📱 Responsive Design

The application is fully responsive with breakpoints:
- Mobile: < 640px
- Tablet: 640px - 1024px
- Desktop: > 1024px

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/)
- [Radix UI](https://www.radix-ui.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [shadcn/ui](https://ui.shadcn.com/)

## 📞 Support

For support, email <EMAIL> or join our Slack channel.

---

Built with ❤️ by DarkHorse Team
