'use client'

import { ChevronLeft } from 'lucide-react'

interface ContactPanelProps {
  onBack: () => void
}

export function ContactPanel({ onBack }: ContactPanelProps) {
  return (
    <div className="w-full flex items-center bg-[#08101e] text-white flex-col">
      <div className="flex w-full justify-between mb-3 sm:mb-4 gap-4">
        <h1 className="text-lg sm:text-xl font-medium">Contact us</h1>
        <button
          onClick={onBack}
          className="flex items-center text-[#bdbac0] hover:text-white transition-colors text-sm sm:text-base"
        >
          <ChevronLeft className="h-4 w-4 sm:h-5 sm:w-5 mr-1" />
          <span>Back</span>
        </button>
      </div>

      <div className="h-px bg-[#313038] w-full mb-4 sm:mb-6"></div>

      <div className="text-center space-y-4">
        <h2 className="text-2xl font-bold">DarkHorseWin</h2>
        <div className="text-sm">
          <p className="mb-2">Email: <EMAIL></p>
          <p>Phone: +****************</p>
        </div>
      </div>
    </div>
  )
}