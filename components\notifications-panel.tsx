'use client'

import { useState } from 'react'
import { ChevronLeft } from 'lucide-react'
import { cn } from '@/lib/utils'

interface NotificationsPanelProps {
  onBack: () => void
}

export function NotificationsPanel({ onBack }: NotificationsPanelProps) {
  const [notificationsEnabled, setNotificationsEnabled] = useState(false)

  return (
    <div className="w-full flex items-center bg-[#08101e] text-white flex-col">
      <div className="flex w-full justify-between mb-3 sm:mb-4 gap-4">
        <h1 className="text-lg sm:text-xl font-medium">Notifications</h1>
        <button 
          onClick={onBack}
          className="flex items-center text-[#bdbac0] hover:text-white transition-colors text-sm sm:text-base"
        >
          <ChevronLeft className="h-4 w-4 sm:h-5 sm:w-5 mr-1" />
          <span>Back</span>
        </button>
      </div>

      <div className="h-px bg-[#313038] w-full mb-4 sm:mb-6"></div>
      <p className="text-center text-[#bdbac0] mb-4 sm:mb-6 text-[12px]">
        Enable notifications to stay updated with the latest predictions and exciting game events directly in your browser
      </p>

      <div className="w-full grid grid-cols-2 gap-0 mb-4 sm:mb-6">
        <button
          onClick={() => setNotificationsEnabled(true)}
          className={cn(
            "py-2 sm:py-3 rounded-l-[8px] font-medium transition-all text-sm sm:text-base relative",
            notificationsEnabled
              ? "text-white"
              : "bg-[#181c23] text-[#bdbac0] hover:bg-[#313038]"
          )}
        >
          {notificationsEnabled && (
            <>
              <div 
                className="absolute inset-0 w-full h-full rounded-l-[8px]" 
                style={{
                  background: "linear-gradient(0deg, rgba(11, 10, 10, 0.4), rgba(0, 0, 0, 0.4)), linear-gradient(261.81deg, rgba(25, 251, 155, 0.4) -8.01%, rgba(140, 1, 250, 0.4) 100%)"
                }}
              />
              <div
                style={{
                  position: "absolute",
                  top: 0,
                  right: 0,
                  bottom: 0,
                  left: 0,
                  borderRadius: "8px",
                  padding: "1.5px",
                  background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                  WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                  WebkitMaskComposite: "xor",
                  maskComposite: "exclude",
                  pointerEvents: "none",
                }}
              />
            </>
          )}
          <span className="relative z-10">Enable</span>
        </button>
        <button
          onClick={() => setNotificationsEnabled(false)}
          className={cn(
            "py-2 sm:py-3 rounded-r-[8px] font-medium transition-all text-sm sm:text-base relative",
            !notificationsEnabled
              ? "text-white"
              : "bg-[#181c23] text-[#bdbac0] hover:bg-[#313038]"
          )}
        >
          {!notificationsEnabled && (
            <>
              <div 
                className="absolute inset-0 w-full h-full rounded-r-[8px]" 
                style={{
                  background: "linear-gradient(0deg, rgba(11, 10, 10, 0.4), rgba(0, 0, 0, 0.4)), linear-gradient(261.81deg, rgba(25, 251, 155, 0.4) -8.01%, rgba(140, 1, 250, 0.4) 100%)"
                }}
              />
              <div
                style={{
                  position: "absolute",
                  top: 0,
                  right: 0,
                  bottom: 0,
                  left: 0,
                  borderRadius: "8px",
                  padding: "1.5px",
                  background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                  WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                  WebkitMaskComposite: "xor",
                  maskComposite: "exclude",
                  pointerEvents: "none",
                }}
              />
            </>
          )}
          <span className="relative z-10">Disable</span>
        </button>
      </div>

      <button className="mt-auto w-full py-2 sm:py-3 rounded-full bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white font-medium hover:opacity-90 transition-opacity text-sm sm:text-base">
        Save
      </button>
    </div>
  )
}




