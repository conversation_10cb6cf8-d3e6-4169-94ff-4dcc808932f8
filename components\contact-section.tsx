export default function ContactSection() {
    return (
        <section id="contacts" className="py-16 md:py-24 relative">
            <div
                className="absolute opacity-30 blur-[150px]"
                style={{
                    width: "362.36px",
                    height: "252.34px",
                    top: "80%",
                    left: "50px",
                    background: "#7F20EF",
                    transform: "rotate(-164.56deg)",
                    zIndex: "0",
                }}
            />
            <div
                className="absolute opacity-30 blur-[150px]"
                style={{
                    width: "362.36px",
                    height: "252.34px",
                    top: "-10%",
                    right: "50px",
                    background: "#2CAD9C",
                    transform: "rotate(-164.56deg)",
                    zIndex: "0",
                }}
            />

            <div className="container px-4 md:px-6 mx-auto text-center">
                <h2 className="text-[28px] md:text-5xl font-bold mb-8 md:mb-12">
                    <span className="bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] bg-clip-text text-transparent">
                        Contact & Support
                    </span>
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-[10px] max-w-[894px] mx-auto mb-16 md:mb-4">
                    <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] p-4 md:p-6 text-center mx-auto">
                        <h3 className="text-white text-[18px] md:text-[20px] font-bold mb-2 md:mb-3">Email</h3>
                        <p className="text-white text-[14px] md:text-[16px]"><EMAIL></p>
                    </div>

                    <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] p-4 md:p-6 text-center mx-auto">
                        <h3 className="text-white text-[18px] md:text-[20px] font-bold mb-2 md:mb-3">Phone</h3>
                        <p className="text-white text-[14px] md:text-[16px]">
                            **************
                        </p>
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-[10px] max-w-[894px] mx-auto">
                    <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] p-4 md:p-6 text-center mx-auto">
                        <h3 className="text-white text-[18px] md:text-[20px] font-bold mb-2 md:mb-3">Live Chat</h3>
                        <p className="text-white text-[14px] md:text-[16px] mb-4 md:mb-6">Available on our website for quick assistance.</p>
                        <div className="flex justify-center">
                            <button className="border border-white/20 bg-transparent text-white text-[14px] font-bold rounded-full w-full sm:w-[244px] h-[48px] flex items-center justify-center hover:bg-white/10 transition-all">
                                Chat with Us
                            </button>
                        </div>
                    </div>

                    <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] p-4 md:p-6 text-center mx-auto">
                        <h3 className="text-white text-[18px] md:text-[20px] font-bold mb-2 md:mb-3">Help Center & FAQs</h3>
                        <p className="text-white text-[14px] md:text-[16px] mb-4 md:mb-6">Get answers to common questions.</p>
                        <div className="flex justify-center">
                            <button className="border border-white/20 bg-transparent text-white text-[14px] font-bold rounded-full w-full sm:w-[244px] h-[48px] flex items-center justify-center hover:bg-white/10 transition-all">
                                Get Answers
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}




