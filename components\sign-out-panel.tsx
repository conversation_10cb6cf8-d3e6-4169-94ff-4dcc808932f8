'use client'

import { ChevronLeft } from 'lucide-react'
import { signOut } from 'aws-amplify/auth'
import { toast } from "sonner"

interface SignOutPanelProps {
  onBack: () => void
}

export function SignOutPanel({ onBack }: SignOutPanelProps) {
  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('Signout successful!', {
        duration: 3000,
        style: {
          background: 'linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)',
          border: '1px solid rgba(25, 251, 155, 0.2)',
          backdropFilter: 'blur(8px)',
        },
      })
      window.location.href = '/login'
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  return (
    <div className="w-full flex items-center bg-[#08101e] text-white flex-col">
      <div className="flex w-full justify-between mb-3 sm:mb-4 gap-4">
        <h1 className="text-lg sm:text-xl font-medium">Sign Out</h1>
        <button 
          onClick={onBack}
          className="flex items-center text-[#bdbac0] hover:text-white transition-colors text-sm sm:text-base"
        >
          <ChevronLeft className="h-4 w-4 sm:h-5 sm:w-5 mr-1" />
          <span>Back</span>
        </button>
      </div>

      <div className="h-px bg-[#313038] w-full mb-4 sm:mb-6"></div>
      
      <p className="text-center text-white mb-4 sm:mb-6 text-[12px]">
        Are you sure you want to sign out? <br/> You'll need to sign in again to access your account.
      </p>

      <button 
        onClick={handleSignOut}
        className="mt-auto w-full py-2 sm:py-3 rounded-full bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white font-medium hover:opacity-90 transition-opacity text-sm sm:text-base"
      >
        Sign Out
      </button>
    </div>
  )
}
