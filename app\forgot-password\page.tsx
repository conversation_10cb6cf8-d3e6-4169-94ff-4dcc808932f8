"use client"

import Image from "next/image"
import Link from "next/link"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { resetPassword, type ResetPasswordOutput } from "aws-amplify/auth"
import { toast } from "sonner"

export default function ForgotPasswordPage() {
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [isFormFilled, setIsFormFilled] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    setIsFormFilled(email.length > 0)
  }, [email])

  const handleResetPasswordNextSteps = (output: ResetPasswordOutput) => {
    const { nextStep } = output
    switch (nextStep.resetPasswordStep) {
      case 'CONFIRM_RESET_PASSWORD_WITH_CODE':
        const { deliveryMedium, destination } = nextStep.codeDeliveryDetails
        toast.success(`Verification code sent to ${deliveryMedium === 'EMAIL' ? 'your email' : destination}`)
        router.push(`/verification-code?email=${encodeURIComponent(email)}`)
        break
      case 'DONE':
        toast.success('Successfully reset password')
        router.push('/login')
        break
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!isFormFilled || isLoading) return

    setIsLoading(true)
    setError(null)

    try {
      const output = await resetPassword({ username: email })
      handleResetPasswordNextSteps(output)
    } catch (error: any) {
      console.error('Reset Password Error:', error)
      setError(error.message)
      toast.error(error.message || 'Failed to send verification code')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen bg-[#050816] p-[30px] gap-[30px] relative overflow-hidden">
      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "10px",
          left: "0%",
          background: "#2CAD9C",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />

      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "450px",
          right: "0%",
          background: "#7F20EF",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />
      <div className="w-full lg:w-1/2 flex items-center justify-center relative z-10">
        <div className="w-full max-w-[520px] flex flex-col items-center">
          <Link href="/" className="flex items-center">
            <div
              className="flex items-center rounded-[100px] w-[188px] h-[40px] px-6 relative mb-8"
              style={{
                gap: "5px",
                background:
                  "linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)",
              }}
            >
              <div
                style={{
                  position: "absolute",
                  top: 0,
                  right: 0,
                  bottom: 0,
                  left: 0,
                  borderRadius: "100px",
                  padding: "1.5px",
                  background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                  WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                  WebkitMaskComposite: "xor",
                  maskComposite: "exclude",
                  pointerEvents: "none",
                }}
              />
              <Image src="https://borisbelov.com/dev/darkhorsewin/darkhorselogo.svg" alt="DarkHorse Icon" width={24} height={26} />
              <Image src="/DARKHORSE.svg" alt="DarkHorse" width={120} height={20} />
            </div>
          </Link>

          <div className="space-y-8">
            <div className="text-center">
              <h1 className="text-[40px] font-bold text-white mb-4">Forgot Password</h1>
              <p className="text-[#9ca3af] text-base md:text-[24px]">Please enter the email address that you used when creating your account</p>
            </div>

            <div className="space-y-4">
              <form onSubmit={handleSubmit}>
                <div className="space-y-2">
                  <label htmlFor="email" className="block text-sm font-medium text-white">
                    Email
                  </label>
                  <div className="relative">
                    <input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full h-12 px-4 rounded-[10px] bg-[#********] backdrop-blur-[20px] border border-[#282828] text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]"
                    />
                  </div>
                </div>

                <button
                  type="submit"
                  className={`w-full h-12 rounded-[70px] backdrop-blur-[20px] transition-all duration-300 flex items-center justify-center gap-[15.16px] mt-6 ${!isFormFilled || isLoading
                      ? "bg-[#28282880] text-[#9ca3af]"
                      : "bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white"
                    }`}
                >
                  {isLoading ? "Sending..." : "Submit"}
                </button>
              </form>

              <div className="text-center pt-4">
                <p className="text-white text-sm">
                  Remember your password?{" "}
                  <Link href="/login" className="text-[#9ca3af] hover:underline">
                    Log in
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="hidden lg:block w-1/2">
        <Image
          src="/login-image.webp"
          alt="Dark Horse App"
          width={800}
          height={900}
          className="w-full h-auto"
          priority
        />
      </div>
    </div>
  )
}



