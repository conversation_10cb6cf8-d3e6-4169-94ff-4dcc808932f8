import Image from "next/image"
import SearchStatusIcon from "./svg/search-status-icon"
import CloudAddIcon from "./svg/cloud-add-icon"
import HierarchyIcon from "./svg/hierarchy-icon"
import DiagramIcon from "./svg/diagram-icon"

export default function HowAiWorks() {
    return (
        <section className="w-full py-20 md:py-28 relative">
            <div className="container mx-auto px-4 max-w-[1200px] relative">
                <div className="text-center mb-6">
                    <h2 className="text-center text-[28px] md:text-5xl font-bold mb-4">
                        <span className="bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] bg-clip-text text-transparent">
                            How Our AI Works
                        </span>
                    </h2>
                </div>

                <div className="text-center mb-10 md:mb-20 max-w-3xl mx-auto">
                    <p className="text-[14px] md:text-[18px] leading-relaxed text-white/80 px-4">
                        Our platform utilizes Generative AI, Retrieval-Augmented Generation (RAG),
                        and Reinforcement Learning models to generate in-depth sports predictions.
                    </p>
                </div>

                <div className="flex flex-col lg:flex-row items-center justify-between mb-20">
                    <div className="w-full lg:w-1/2 mb-8 lg:mb-0 text-left">
                        <h3 className="text-[18px] md:text-[26px] font-bold mb-8 md:mb-4 text-center lg:text-left text-white">
                            Core AI Features
                        </h3>

                        <div className="grid grid-cols-2 gap-8 md:hidden">
                            <div className="flex flex-col items-center text-center">
                                <SearchStatusIcon className="h-10 w-10 mb-4" />
                                <h3 className="text-white text-[14px] font-bold">Predictive</h3>
                                <h3 className="text-white text-[14px] font-bold mb-3">Modeling</h3>
                                <p className="text-white text-[12px]">
                                    We analyze <span className="font-bold">team performance, player stats, historical matchups, and game conditions</span> to forecast game outcomes.
                                </p>
                            </div>

                            <div className="flex flex-col items-center text-center">
                                <DiagramIcon className="h-10 w-10 mb-4" />
                                <h3 className="text-white text-[14px] font-bold">Confidence</h3>
                                <h3 className="text-white text-[14px] font-bold mb-3">Metrics</h3>
                                <p className="text-white text-[12px]">
                                    Every prediction includes <span className="font-bold">a confidence score</span>, helping users gauge reliability.
                                </p>
                            </div>

                            <div className="flex flex-col items-center text-center">
                                <CloudAddIcon className="h-10 w-10 mb-4" />
                                <h3 className="text-white text-[14px] font-bold">Cloud-Based Speed</h3>
                                <h3 className="text-white text-[14px] font-bold mb-3">& Security</h3>
                                <p className="text-white text-[12px]">
                                    Our models run on <span className="font-bold">AWS infrastructure</span>, ensuring fast and scalable performance.
                                </p>
                            </div>

                            <div className="flex flex-col items-center text-center">
                                <HierarchyIcon className="h-10 w-10 mb-4" />
                                <h3 className="text-white text-[14px] font-bold">API</h3>
                                <h3 className="text-white text-[14px] font-bold mb-3">Integrations</h3>
                                <p className="text-white text-[12px]">
                                    Seamless third-party integration for sportsbooks, media platforms, and fantasy leagues.
                                </p>
                            </div>
                        </div>

                        <div className="hidden md:grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-8">
                            <div>
                                <SearchStatusIcon className="h-8 w-8 mb-4" />
                                <h3 className="text-white text-[20px] font-bold">Predictive</h3>
                                <h3 className="text-white text-[20px] font-bold mb-3">Modeling</h3>
                                <p className="text-white text-[14px] max-w-[280px]">
                                    We analyze <span className="font-bold">team performance, player stats, historical matchups, and game conditions</span> to forecast game outcomes.
                                </p>
                            </div>

                            <div>
                                <DiagramIcon className="h-8 w-8 mb-4" />
                                <h3 className="text-white text-[20px] font-bold">Confidence</h3>
                                <h3 className="text-white text-[20px] font-bold mb-3">Metrics</h3>
                                <p className="text-white text-[14px] max-w-[280px]">
                                    Every prediction includes a <span className="font-bold">confidence score</span>, helping users gauge reliability.
                                </p>
                            </div>

                            <div>
                                <CloudAddIcon className="h-8 w-8 mb-4" />
                                <h3 className="text-white text-[20px] font-bold">Cloud-Based Speed</h3>
                                <h3 className="text-white text-[20px] font-bold mb-3">& Security</h3>
                                <p className="text-white text-[14px] max-w-[280px]">
                                    Our models run on <span className="font-bold">AWS infrastructure</span>, ensuring fast and scalable performance.
                                </p>
                            </div>

                            <div>
                                <HierarchyIcon className="h-8 w-8 mb-4" />
                                <h3 className="text-white text-[20px] font-bold">API</h3>
                                <h3 className="text-white text-[20px] font-bold mb-3">Integrations</h3>
                                <p className="text-white text-[14px] max-w-[280px]">
                                    Seamless third-party integration for sportsbooks, media platforms, and fantasy leagues.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="w-full lg:w-1/2">
                        <div className="relative w-full">
                              <Image
                                src="/frame-about.webp"
                                alt="Card Frame About"
                                width={620}
                                height={431}
                                className="w-full h-auto"
                              />
                            </div>
                    </div>
                </div>

                <div className="mt-20 text-center">
                    <p className="text-white text-[18px] md:text-[22px] mb-6 mx-auto max-w-[729px]">
                        Our AI system is built to continuously improve based on game results,
                        meaning our predictions get smarter over time.
                    </p>
                </div>
            </div>
        </section>
    )
}
