import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { headers } from 'next/headers';
import { UserService } from '@/services/api/users';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

export async function POST(req: NextRequest) {
  const body = await req.text();
  const signature = headers().get('stripe-signature') as string;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (error: any) {
    console.error(`Webhook signature verification failed: ${error.message}`);
    return NextResponse.json({ error: error.message }, { status: 400 });
  }

  // Handle the event
  if (event.type === 'checkout.session.completed') {
    const session = event.data.object as Stripe.Checkout.Session;
    
    // Extract metadata
    const userId = session.metadata?.userId;
    const creditType = session.metadata?.creditType;
    const amount = parseInt(session.metadata?.amount || '0');

    if (userId && creditType && amount) {
      try {
        // Update user credits in your database
        // This is a placeholder - implement according to your API
        if (creditType === 'first') {
          // Update AI chat credits
          await UserService.updateUserCredits(userId, { aiChatCredits: amount });
        } else if (creditType === 'second') {
          // Update prediction credits
          await UserService.updateUserCredits(userId, { predictionCredits: amount });
        }
        
        console.log(`Added ${amount} ${creditType} credits to user ${userId}`);
      } catch (error) {
        console.error('Error updating user credits:', error);
        return NextResponse.json(
          { error: 'Error updating user credits' },
          { status: 500 }
        );
      }
    }
  }

  return NextResponse.json({ received: true });
}

export const config = {
  api: {
    bodyParser: false,
  },
};