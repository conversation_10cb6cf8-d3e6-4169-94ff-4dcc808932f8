
import { useState, useEffect } from "react"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"
import ChatAIModal from "@/components/chat-ai-modal"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import RightAsideContent from "@/components/right-aside-content"

export default function FloatingActionButton() {
  const [isOpen, setIsOpen] = useState(false)
  const [isChatOpen, setIsChatOpen] = useState(false)
  const [showRightAsideModal, setShowRightAsideModal] = useState(false)
  const [currentView, setCurrentView] = useState<'slip' | 'profile' | 'notifications' | 'settings' | 'help-info' | 'contact' | 'sign-out'>('slip')

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsOpen(false)
        setIsChatOpen(false)
      }
    }

    window.addEventListener('keydown', handleEscape)
    return () => window.removeEventListener('keydown', handleEscape)
  }, [])

  useEffect(() => {
    if (isChatOpen) {
      setIsOpen(false)
    }
  }, [isChatOpen])

  const handleChatClick = () => {
    setIsChatOpen(true)
  }

  return (
    <>
      <div className="fixed bottom-6 right-6 z-50">
        <AnimatePresence>
          {isOpen && (
            <>
              {window.innerWidth < 1280 && (
                <motion.div
                  className="absolute bottom-16 right-0 cursor-pointer"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Image
                    src="/purchase-button.svg"
                    alt="Purchase"
                    width={52}
                    height={52}
                    style={{ width: 'auto', height: 'auto' }}
                    className="drop-shadow-lg hover:scale-105 transition-transform"
                    onClick={() => {
                      setCurrentView('slip');
                      setShowRightAsideModal(true);
                    }}
                  />
                </motion.div>
              )}

              <motion.div
                className="absolute bottom-0 left-[-65px] cursor-pointer"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.2, delay: 0.1 }}
              >
                <Image
                  src="/chat-ai.svg"
                  alt="Chat AI"
                  width={52}
                  height={52}
                  style={{ width: 'auto', height: 'auto' }}
                  className="drop-shadow-lg hover:scale-105 transition-transform"
                  onClick={handleChatClick}
                />
              </motion.div>
            </>
          )}
        </AnimatePresence>

        <motion.div
          className="cursor-pointer relative z-10"
          whileTap={{ scale: 0.95 }}
          onClick={() => setIsOpen(!isOpen)}
        >
          <AnimatePresence mode="wait">
            {isOpen ? (
              <motion.div
                key="close"
                initial={{ rotate: -90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: 90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Image
                  src="/main-button-close.svg"
                  alt="Close Menu"
                  width={52}
                  height={52}
                  style={{ width: 'auto', height: 'auto' }}
                  className="drop-shadow-lg"
                />
              </motion.div>
            ) : (
              <motion.div
                key="open"
                initial={{ rotate: 90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: -90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Image
                  src="/main-button.svg"
                  alt="Open Menu"
                  width={52}
                  height={52}
                  style={{ width: 'auto', height: 'auto' }}
                  className="drop-shadow-lg"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>

      <ChatAIModal
        isOpen={isChatOpen}
        onClose={() => setIsChatOpen(false)}
      />

      <Dialog open={showRightAsideModal} onOpenChange={setShowRightAsideModal}>
        <DialogContent className="sm:max-w-[425px] bg-[#08101e] border-none p-6 text-white">
          <div className="relative z-10">
            <RightAsideContent
              currentView={currentView}
              onViewChange={setCurrentView}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}








