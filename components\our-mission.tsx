import Image from "next/image"

export default function OurMission() {
    return (
        <section className="w-full py-8 md:py-24">
            <div className="container mx-auto px-4">
                <div className="flex justify-center mb-16">
                    <div className="bg-[#28282847] backdrop-blur-[20px] rounded-full px-6 py-2">
                        <span className="text-[#99ACEA] font-['Inter'] text-[14px]">Home / </span>
                        <span className="text-white font-['Inter'] text-[14px]">About Us</span>
                    </div>
                </div>

                <h1 className="text-[28px] md:text-6xl font-bold text-center mb-16">
                    <span
                        style={{
                            background:
                                "linear-gradient(0deg, #FFFFFF, #FFFFFF), linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                            WebkitBackgroundClip: "text",
                            WebkitTextFillColor: "transparent",
                            backgroundClip: "text",
                        }}
                    >
                        Our{" "}
                    </span>
                    <span className="gradient-text">
                        Mission
                    </span>
                </h1>

                <div className="max-w-[1244px] mx-auto text-center">
                    <p className="text-[14px] md:text-[22px] leading-relaxed text-white/80 space-y-6">
                        <span className="block mb-6">
                            DarkHorseWin.com is a cutting-edge AI-powered sports analytics platform that delivers data-driven insights to help sports enthusiasts, bettors, and analysts make informed decisions. Unlike traditional sportsbooks, we do not set odds or take bets—we provide world-class AI-generated predictions based on real data, free from bookmaker bias.
                        </span>
                        <span className="block">
                            Our goal is simple: Empower users with transparent, high-accuracy predictions for the NFL, NBA, MLB, and MLS.
                        </span>
                    </p>
                </div>
            </div>
        </section>
    )
}

