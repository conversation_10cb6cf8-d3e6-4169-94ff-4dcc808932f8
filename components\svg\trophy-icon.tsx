export default function TrophyIcon({ className = "" }: { className?: string }) {
  return (
    <svg width="36" height="37" viewBox="0 0 36 37" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
      <path d="M18.7952 21.8265C14.3252 21.8265 10.7102 18.2115 10.7102 13.7415V4.82654H26.8802V13.7415C26.8802 18.2115 23.2652 21.8265 18.7952 21.8265Z" stroke="white" strokeWidth="2.25" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M27.7051 13.7415C29.8051 13.7415 31.5001 12.0465 31.5001 9.94654V8.52154C31.5001 7.24654 30.4651 6.21154 29.1901 6.21154H26.8801" stroke="white" strokeWidth="2.25" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M9.89508 13.7415C7.79508 13.7415 6.10008 12.0465 6.10008 9.94654V8.52154C6.10008 7.24654 7.13508 6.21154 8.41008 6.21154H10.7201" stroke="white" strokeWidth="2.25" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M13.25 25.8265H24.34" stroke="white" strokeWidth="2.25" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M15.5 29.8265H22.09" stroke="white" strokeWidth="2.25" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M18.795 21.8265V24.8265" stroke="white" strokeWidth="2.25" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M18.795 24.8265V29.8265" stroke="white" strokeWidth="2.25" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  )
}
