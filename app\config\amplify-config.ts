import { AuthConfig } from "@aws-amplify/core";
import { ResourcesConfig } from "aws-amplify";

if (!process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID || !process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID) {
  throw new Error('Required Cognito configuration is missing');
}

export const amplifyConfig: ResourcesConfig = {
  Auth: {
    Cognito: {
      userPoolId: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID,
      userPoolClientId: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID,
      loginWith: {
        oauth: {
          domain: 'darkhorsewin-auth-domain.auth.us-east-2.amazoncognito.com',
          scopes: ['aws.cognito.signin.user.admin', 'email', 'openid', 'phone', 'profile' ],
          redirectSignIn: [
            'https://darkhorsewin.com'
          ],
          redirectSignOut: [
            'https://darkhorsewin.com/login'
          ],
          responseType: 'token',
          providers: ['Google', 'Facebook'],
        },
        username: true,
        email: true,
        phone: true,
      }
    }
  }
};

