"use client"

import Image from "next/image"
import Link from "next/link"
import { useState, useEffect, Suspense } from "react"
import { useRouter } from "next/navigation"
import { Eye, EyeOff } from "lucide-react"
import { confirmResetPassword, type ConfirmResetPasswordInput } from "aws-amplify/auth"
import { toast } from "sonner"
import { useSearchParams } from "next/navigation"

function ResetPasswordForm() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const email = searchParams.get('email') || ''
  const code = searchParams.get('code') || ''
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isFormFilled, setIsFormFilled] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    setIsFormFilled(
      password.length >= 8 && 
      password === confirmPassword
    )
  }, [password, confirmPassword])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!isFormFilled || isLoading) return

    if (password !== confirmPassword) {
      toast.error('Passwords do not match')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      await confirmResetPassword({
        username: email,
        confirmationCode: code,
        newPassword: password
      })
      toast.success('Password reset successfully')
      router.push('/login')
    } catch (error: any) {
      console.error('Reset Password Error:', error)
      setError(error.message)
      toast.error(error.message || 'Failed to reset password')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen bg-[#050816] p-[30px] gap-[30px] relative overflow-hidden">
      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "10px",
          left: "0%",
          background: "#2CAD9C",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />

      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "450px",
          right: "0%",
          background: "#7F20EF",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />
      <div className="w-full lg:w-1/2 flex items-center justify-center relative z-10">
        <div className="w-full max-w-[520px] flex flex-col items-center">
          <Link href="/" className="flex items-center">
            <div
              className="flex items-center rounded-[100px] w-[188px] h-[40px] px-6 relative mb-8"
              style={{
                gap: "5px",
                background:
                  "linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)",
              }}
            >
              <div
                style={{
                  position: "absolute",
                  top: 0,
                  right: 0,
                  bottom: 0,
                  left: 0,
                  borderRadius: "100px",
                  padding: "1.5px",
                  background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                  WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                  WebkitMaskComposite: "xor",
                  maskComposite: "exclude",
                  pointerEvents: "none",
                }}
              />
              <Image src="https://borisbelov.com/dev/darkhorsewin/darkhorselogo.svg" alt="DarkHorse Icon" width={24} height={26} />
              <Image src="/DARKHORSE.svg" alt="DarkHorse" width={120} height={20} />
            </div>
          </Link>

          <div className="space-y-8">
            <div className="text-center">
              <h1 className="text-[28px] md:text-[40px] font-bold text-white mb-4">Create New Password</h1>
              <p className="text-[#9ca3af] text-base md:text-[24px]">Your new password must be different from previous used passwords</p>
            </div>

            <div className="space-y-4">
              <form onSubmit={handleSubmit}>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label htmlFor="password" className="block text-sm font-medium text-white">
                      Password
                    </label>
                    <div className="relative">
                      <input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="At least 8 characters"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border border-[#282828] text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
                      >
                        {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                      </button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-white">
                      Confirm Password
                    </label>
                    <div className="relative">
                      <input
                        id="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        placeholder="Confirm your password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border border-[#282828] text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]"
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
                      >
                        {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                      </button>
                    </div>
                  </div>
                </div>

                <button
                  type="submit"
                  className={`w-full h-12 rounded-[70px] backdrop-blur-[20px] transition-all duration-300 flex items-center justify-center gap-[15.16px] mt-6 ${isFormFilled
                      ? "bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white"
                      : "bg-[#28282880] text-[#9ca3af]"
                    }`}
                >
                  {isLoading ? "Resetting Password..." : "Reset Password"}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>

      <div className="hidden lg:block w-1/2">
        <Image
          src="/login-image.webp"
          alt="Dark Horse App"
          width={800}
          height={900}
          className="w-full h-auto"
          priority
        />
      </div>
    </div>
  )
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center bg-[#050816]">
        <div className="text-white">Loading...</div>
      </div>
    }>
      <ResetPasswordForm />
    </Suspense>
  )
}
