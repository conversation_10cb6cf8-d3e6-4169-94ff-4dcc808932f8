import { API_BASE_URL, API_ENDPOINTS } from './config';
import type { ApiResponse } from '@/types/api';
import type {
  NFLGameKey,
  NFLBatchPredictionRequest,
  NFLPredictionUploadRequest,
  NFLTrainModelRequest,
  NFLTrainRlAgentRequest,
  NFLPredictUpcomingGamesRequest,
  NFLPick6Request
} from '@/types/nfl';

export class NFLService {
  static async uploadPredictions(
    csvPath: string,
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nfl.predictions.upload}?token=${encodeURIComponent(token)}`;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ csv_path: csvPath } as NFLPredictionUploadRequest),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to upload NFL predictions',
        },
      };
    }
  }

  static async getGamesByWeek(
    weekNumber: number,
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nfl.predictions.getByWeek(weekNumber)}?token=${encodeURIComponent(token)}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: `Failed to get NFL games for week ${weekNumber}`,
        },
      };
    }
  }

  static async getPredictionsByGameIds(
    userId: string,
    gameKeys: NFLGameKey[],
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nfl.predictions.getByGameIds}?token=${encodeURIComponent(token)}`;

      const requestBody: NFLBatchPredictionRequest = {
        user_id: userId,
        keys: gameKeys
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to get NFL predictions for the specified games',
        },
      };
    }
  }

  static async trainModel(
    trainingSeasons: number[] = [2023, 2024],
    currentWeek: number = 10,
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nfl.trainModel}?token=${encodeURIComponent(token)}`;

      const requestBody: NFLTrainModelRequest = {
        training_seasons: trainingSeasons,
        current_week: currentWeek
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to train NFL model',
        },
      };
    }
  }

  static async trainRlAgent(
    trainingSeasons: number[] = [2023, 2024],
    currentWeek: number = 10,
    numSelect: number = 6,
    maxNumGames: number = 76,
    timesteps: number = 150000,
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nfl.trainRlAgent}?token=${encodeURIComponent(token)}`;

      const requestBody: NFLTrainRlAgentRequest = {
        training_seasons: trainingSeasons,
        current_week: currentWeek,
        num_select: numSelect,
        max_num_games: maxNumGames,
        timesteps: timesteps
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to train NFL RL agent',
        },
      };
    }
  }

  static async predictUpcomingGames(
    trainingSeasons: number[] = [2023, 2024],
    currentWeek: number = 10,
    predictionSeason: string = "2024POST",
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nfl.predictUpcomingGames}?token=${encodeURIComponent(token)}`;

      const requestBody: NFLPredictUpcomingGamesRequest = {
        training_seasons: trainingSeasons,
        current_week: currentWeek,
        prediction_season: predictionSeason
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to predict upcoming NFL games',
        },
      };
    }
  }

  static async getPick6(
    numSelect: number = 6,
    maxNumGames: number = 96,
    currentWeek: number = 10,
    token: string
  ): Promise<ApiResponse<string>> {
    try {
      const url = `${API_BASE_URL}${API_ENDPOINTS.nfl.pick6}?token=${encodeURIComponent(token)}`;

      const requestBody: NFLPick6Request = {
        num_select: numSelect,
        max_num_games: maxNumGames,
        current_week: currentWeek
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to get NFL Pick6 selections',
        },
      };
    }
  }
}


