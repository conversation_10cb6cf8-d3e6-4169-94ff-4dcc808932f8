import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Bell, Settings, HelpCircle, Phone, LogOut, ChevronRight } from "lucide-react"

interface ProfileMenuProps {
  name: string;
  email: string;
  onNotificationsClick: () => void;
  onSettingsClick: () => void;
  onHelpInfoClick: () => void;
  onContactClick: () => void;
  onSignOutClick: () => void;
}

function MenuItem({ 
  icon, 
  label, 
  onClick 
}: { 
  icon: React.ReactNode; 
  label: string;
  onClick?: () => void;
}) {
  return (
    <button 
      onClick={onClick}
      className="w-full flex items-center justify-between p-3 rounded-[8px] bg-[#28282880] hover:bg-[#282828] transition-colors"
    >
      <div className="flex items-center gap-3">
        {icon}
        <span className="text-sm">{label}</span>
      </div>
      <ChevronRight className="h-5 w-5 text-[#a8a3ac]" />
    </button>
  );
}

export function ProfileMenu({ 
  name, 
  email, 
  onNotificationsClick,
  onSettingsClick,
  onHelpInfoClick,
  onContactClick,
  onSignOutClick
}: ProfileMenuProps) {
  return (
    <div className="max-w-md mx-auto">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-1.5 sm:gap-2">
          <Avatar className="h-12 w-12 md:h-12 md:w-12 border-2 border-[#282828]">
            <AvatarImage src="/sample-avatar.svg?height=48&width=48" alt={name} />
            <AvatarFallback className="bg-[#181c23] text-sm sm:text-base">
              {name
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>
          <div>
            <h1 className="text-lg sm:text-xl font-bold">{name}</h1>
            <p className="text-[#a8a3ac] text-xs sm:text-sm">{email}</p>
          </div>
        </div>
        <Button
          variant="outline"
          className="rounded-full px-4 py-2 text-xs sm:text-sm border-[#A8A3AC] bg-transparent text-[#fcfcfd] hover:bg-[#181c23] hover:text-[#ffffff]"
        >
          Edit
        </Button>
      </div>

      <div className="space-y-2">
        <MenuItem 
          icon={<Bell className="h-6 w-6" />} 
          label="Notifications" 
          onClick={onNotificationsClick}
        />
        <MenuItem 
          icon={<Settings className="h-5 w-5" />} 
          label="Settings" 
          onClick={onSettingsClick}
        />
        <MenuItem 
          icon={<HelpCircle className="h-5 w-5" />} 
          label="Help & Info" 
          onClick={onHelpInfoClick}
        />
        <MenuItem 
          icon={<Phone className="h-5 w-5" />} 
          label="Contact us" 
          onClick={onContactClick}
        />
        <MenuItem 
          icon={<LogOut className="h-5 w-5" />} 
          label="Sign out" 
          onClick={onSignOutClick}
        />
      </div>
    </div>
  )
}







