"use client"

import { useState } from 'react'
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { useIsMobile } from "@/hooks/use-mobile"
import RightAsideContent from '@/components/right-aside-content'
import MainHeader from '@/components/main-header'
import LeftSidebar from '@/components/left-sidebar'
import { ChevronLeft, Star } from 'lucide-react'
import Image from 'next/image'
import { ScrollArea } from "@/components/ui/scroll-area"
import { useRouter } from 'next/navigation'
import FloatingActionButton from "@/components/floating-action-button"
import NBAIcon from "@/components/svg/nba-team-icon"

export default function GamePage() {
  const router = useRouter()
  const isMobile = useIsMobile();
  const [currentView, setCurrentView] = useState<'slip' | 'profile' | 'notifications' | 'settings' | 'help-info' | 'contact' | 'sign-out'>('slip');
  const [selectedButton, setSelectedButton] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('team-metrics');

  const isSelected = (buttonId: string) => selectedButton === buttonId;

  const handleButtonClick = (buttonId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedButton(selectedButton === buttonId ? null : buttonId);
  };
  const handleProfileClick = () => {
    if (!isMobile) {
      setCurrentView('profile');
    }
  };

  const handleBack = () => {
    router.back()
  }

  return (
    <div className="relative">
      <div className="md:hidden">
        <div
          className="absolute opacity-30 blur-[150px] pointer-events-none"
          style={{
            width: "362.36px",
            height: "252.34px",
            top: "10px",
            left: "0%",
            background: "#2CAD9C",
            transform: "rotate(-164.56deg)",
            zIndex: "0",
          }}
        />

        <div
          className="absolute opacity-30 blur-[150px] pointer-events-none"
          style={{
            width: "362.36px",
            height: "252.34px",
            top: "450px",
            right: "60px",
            background: "#7F20EF",
            transform: "rotate(-164.56deg)",
            zIndex: "0",
          }}
        />
      </div>

      <div className="flex h-screen">
        <LeftSidebar />

        <div className="flex-1 overflow-hidden bg-[#070F1C]">
          <MainHeader onProfileClick={handleProfileClick} />
          <div className="flex flex-1 h-[calc(100vh-64px)] overflow-hidden">
            <main className="flex-1 bg-[#050816]">

                <div className="p-4 md:p-6 m flex items-center justify-between">
                  <button 
                    onClick={handleBack}
                    className="flex items-center text-[10px] sm:text-[12px] text-[#9CA2B5] bg-[#08101E] px-2 sm:px-3 py-2 rounded-full font-bold"
                  >
                    <ChevronLeft size={16} className="mr-0 sm:mr-1" />
                    <span className="hidden sm:inline">BACK</span>
                  </button>

                  <div className="text-center">
                    <h1 className="text-[14px] sm:text-xl">DEN Nuggets @ IND Pacers</h1>
                  </div>

                  <button className="flex items-center text-[10px] sm:text-[12px] text-[#9CA2B5] bg-[#08101E] px-2 sm:px-3 py-2 rounded-full font-bold">
                    <Star size={16} className="mr-0 sm:mr-1" />
                    <span className="hidden sm:inline">ADD TO FAVORITES</span>
                  </button>
                </div>

                <div className="flex justify-center items-center py-4 px-2 md:px-8">
                  <div className="flex items-center">
                    <div className="text-right mr-2 md:mr-4 text-[10px] md:text-[14px] text-gray-400">
                      <div className="">DEN</div>
                      <div className="text-[14px] md:text-[20px] text-white font-bold py-1">Nuggets</div>
                      <div className="">37-20</div>
                    </div>

                    <NBAIcon teamName="Denver Nuggets" className="w-12 h-12" />
                  </div>

                  <div className="mx-4 md:mx-8 text-center text-[10px] md:text-[14px] text-gray-400">
                    <div className="">FEB. 24, 2025</div>
                    <div className="text-[14px] md:text-[20px] text-white font-bold py-1">5:00pm</div>
                    <div className="">DET -1.5 | O/U 221.5</div>
                  </div>

                  <div className="flex items-center">
                    <NBAIcon teamName="Indiana Pacers" className="w-12 h-12" />

                    <div className="text-left ml-2 md:ml-4 text-[10px] md:text-[14px] text-gray-400">
                      <div className="">IND</div>
                      <div className="text-[14px] md:text-[20px] text-white font-bold py-1">Pacers</div>
                      <div className="">32-23</div>
                    </div>
                  </div>
                </div>

                <div>
                  <div className="grid grid-cols-3 gap-2 lg:hidden tab-button-container p-2 sm:p-3 md:p-4">
                    <button 
                      className={`tab-button text-xs sm:text-sm md:text-base ${activeTab === 'team-metrics' ? 'active' : ''}`}
                      onClick={() => setActiveTab('team-metrics')}
                    >
                      <span className="relative z-10">TEAM METRICS</span>
                    </button>
                    <button 
                      className={`tab-button text-xs sm:text-sm md:text-base ${activeTab === 'preview' ? 'active' : ''}`}
                      onClick={() => setActiveTab('preview')}
                    >
                      <span className="relative z-10">PREVIEW</span>
                    </button>
                    <button 
                      className={`tab-button text-xs sm:text-sm md:text-base ${activeTab === 'game-metrics' ? 'active' : ''}`}
                      onClick={() => setActiveTab('game-metrics')}
                    >
                      <span className="relative z-10">GAME METRICS</span>
                    </button>
                  </div>

                  <div className="hidden lg:grid grid-cols-3 border-y border-[#2D3643] border-solid p-3 md:p-4 lg:p-5 text-center text-sm md:text-base text-gray-400 font-bold">
                    <h2 className="border-r border-[#2D3643]">TEAM METRICS</h2>
                    <h2 className="border-r border-[#2D3643]">PREVIEW</h2>
                    <h2>GAME METRICS</h2>
                  </div>


                  <ScrollArea className={`
                    ${isMobile 
                      ? 'h-[calc(100vh-280px)] overflow-hidden' 
                      : 'h-[calc(100vh-380px)] overflow-y-auto'
                    }
                  `}>
                <style jsx global>{`
                  .scrollbar-thumb {
                    background: linear-gradient(38.98deg, #19FB9B -34.05%, #8C01FA 77.64%) !important;
                    width: 2px !important;
                    border-radius: 88px !important;
                  }
                `}</style>
                  <div className="p-3 md:p-4 lg:p-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
                      <div className={`tab-content ${activeTab === 'team-metrics' ? 'active' : ''}`}>
                        <div>
                          <div className="flex justify-between items-center mb-6">
                            <h2 className="text-base text-[14px] font-semibold">TEAM METRICS</h2>
                            <div className="flex items-center gap-1">
                              <span className="text-[12px] text-gray-400">DarkHorse AI Prediction</span>
                              <Image
                                src="/prediction-icon.svg"
                                alt="Prediction Icon"
                                width={15}
                                height={15}
                              />
                            </div>
                          </div>
                          <div className='flex flex-col mb-4'>
                            <div className="flex justify-between gap-2 mb-2 text-[12px]">
                              <div className="font-bold text-[#19FB9B]">28.5</div>
                              <div className="font-semibold">BATTING AVERAGE(AVG)</div>
                              <div className="text-gray-400">13</div>
                            </div>
                            <div className="flex justify-between gap-2">
                              <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 right-0 h-full w-[85%] gradient-progress" />
                              </div>
                              <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 left-0 h-full w-[100%] normal-progress" />
                              </div>
                            </div>
                          </div>

                          <div className='flex flex-col mb-4'>
                            <div className="flex justify-between gap-2 mb-2 text-[12px]">
                              <div className="font-bold text-[#19FB9B]">245.5</div>
                              <div className="font-semibold">HOME RUNS (HR)</div>
                              <div className="text-gray-400">185.3</div>
                            </div>
                            <div className="flex justify-between gap-2">
                              <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 right-0 h-full w-[35%] gradient-progress" />
                              </div>
                              <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 left-0 h-full w-[70%] normal-progress" />
                              </div>
                            </div>
                          </div>

                          <div className='flex flex-col mb-4'>
                            <div className="flex justify-between gap-2 mb-2 text-[12px]">
                              <div className="font-bold text-[#19FB9B]">28.5</div>
                              <div className="font-semibold">RUNS PER GAME (RPG)</div>
                              <div className="text-gray-400">13</div>
                            </div>
                            <div className="flex justify-between gap-2">
                              <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 right-0 h-full w-[95%] gradient-progress" />
                              </div>
                              <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 left-0 h-full w-[10%] normal-progress" />
                              </div>
                            </div>
                          </div>

                          <div className='flex flex-col mb-4'>
                            <div className="flex justify-between gap-2 mb-2 text-[12px]">
                              <div className="font-bold text-[#19FB9B]">28.5</div>
                              <div className="font-semibold">EARNED RUN AVERAGE (ERA)</div>
                              <div className="text-gray-400">13</div>
                            </div>
                            <div className="flex justify-between gap-2">
                              <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 right-0 h-full w-[15%] gradient-progress" />
                              </div>
                              <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 left-0 h-full w-[90%] normal-progress" />
                              </div>
                            </div>
                          </div>

                          <div className='flex flex-col mb-4'>
                            <div className="flex justify-between gap-2 mb-2 text-[12px]">
                              <div className="font-bold text-[#19FB9B]">28.5</div>
                              <div className="font-semibold">STRIKEOUTS PER GAME (K/9)</div>
                              <div className="text-gray-400">13</div>
                            </div>
                            <div className="flex justify-between gap-2">
                              <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 right-0 h-full w-[45%] gradient-progress" />
                              </div>
                              <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 left-0 h-full w-[40%] normal-progress" />
                              </div>
                            </div>
                          </div>

                          <div className='flex flex-col mb-4'>
                            <div className="flex justify-between gap-2 mb-2 text-[12px]">
                              <div className="font-bold text-[#19FB9B]">28.5</div>
                              <div className="font-semibold">WALKS ALLOWED PER GAME (BB/9)</div>
                              <div className="text-gray-400">13</div>
                            </div>
                            <div className="flex justify-between gap-2">
                              <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 right-0 h-full w-[15%] gradient-progress" />
                              </div>
                              <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 left-0 h-full w-[80%] normal-progress" />
                              </div>
                            </div>
                          </div>

                          <div className='flex flex-col mb-4'>
                            <div className="flex justify-between gap-2 mb-2 text-[12px]">
                              <div className="font-bold text-[#19FB9B]">28.5</div>
                              <div className="font-semibold">FILEDING PERCENTAGE (FPCT)</div>
                              <div className="text-gray-400">13</div>
                            </div>
                            <div className="flex justify-between gap-2">
                              <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 right-0 h-full w-[75%] gradient-progress" />
                              </div>
                              <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 left-0 h-full w-[20%] normal-progress" />
                              </div>
                            </div>
                          </div>

                          <div className='flex flex-col mb-4'>
                            <div className="flex justify-between gap-2 mb-2 text-[12px]">
                              <div className="font-bold text-[#19FB9B]">28.5</div>
                              <div className="font-semibold">ERRORS (E)</div>
                              <div className="text-gray-400">13</div>
                            </div>
                            <div className="flex justify-between gap-2">
                              <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 right-0 h-full w-[55%] gradient-progress" />
                              </div>
                              <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 left-0 h-full w-[50%] normal-progress" />
                              </div>
                            </div>
                          </div>

                          <div className='flex flex-col mb-4'>
                            <div className="flex justify-between gap-2 mb-2 text-[12px]">
                              <div className="font-bold text-[#19FB9B]">28.5</div>
                              <div className="font-semibold">ON-BASE PERCENTAGE (OBP)</div>
                              <div className="text-gray-400">13</div>
                            </div>
                            <div className="flex justify-between gap-2">
                              <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 right-0 h-full w-[15%] gradient-progress" />
                              </div>
                              <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 left-0 h-full w-[80%] normal-progress" />
                              </div>
                            </div>
                          </div>

                          <div className='flex flex-col mb-4'>
                            <div className="flex justify-between gap-2 mb-2 text-[12px]">
                              <div className="font-bold text-[#19FB9B]">28.5</div>
                              <div className="font-semibold">DIVISION STANDINGS & PLAYOFF ODDS</div>
                              <div className="text-gray-400">13</div>
                            </div>
                            <div className="flex justify-between gap-2">
                              <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 right-0 h-full w-[85%] gradient-progress" />
                              </div>
                              <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                                <div className="absolute top-0 left-0 h-full w-[20%] normal-progress" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className={`tab-content ${activeTab === 'preview' ? 'active' : ''}`}>
                        <div
                          className="rounded-[12px] bg-[#3131314D] text-[#FFFFFFB2] cursor-pointer hover:bg-[#3131316D] transition-colors mb-4"
                        >
                          <div className="flex items-center justify-between p-2">
                            <h2 className="text-[17px] text-white">Consensus Odds</h2>
                            <div className="flex items-center gap-1.5">
                              <div className="flex items-center gap-1.5 bg-[#171E2B] rounded-[50px] px-2 py-1.5 text-[10px] sm:text-[11px] text-gray-400">
                                <Image
                                  src="/notification.svg"
                                  alt="Notification Icon"
                                  width={14}
                                  height={14}
                                  style={{ width: 'auto', height: 'auto' }}
                                />
                                <span>TODAY 5:00 PM</span>
                              </div>
                            </div>
                          </div>

                          <div className="grid grid-cols-4 p-1.5 text-center text-[10px] sm:text-[11px]">
                            <div></div>
                            <div>SPREAD</div>
                            <div>MONEYLINE</div>
                            <div>TOTAL</div>
                          </div>

                          <div className="grid grid-cols-4 p-1.5">
                            <div className="flex flex-col text-center p-1 text-white text-[10px] sm:text-[11px]">
                              <div className='pb-2'>DEN Nuggets</div>
                              <div>IND Pacers</div>
                            </div>

                            <div className="flex flex-col items-center p-0.5 text-[10px] sm:text-[11px]">
                              <button
                                onClick={(e) => handleButtonClick('spread-button', e)}
                                className={`relative mb-1 w-full rounded-[4px] p-1 text-center ${isSelected('spread-button')
                                  ? 'bg-transparent'
                                  : 'bg-[#171E2B] hover:bg-[#28303f]'
                                  }`}
                              >
                                <span className="relative z-10">
                                  -5 {" "}
                                </span>
                                <span className="relative z-10 text-white">
                                  -110
                                </span>
                              </button>
                              <button className="w-full rounded bg-[#171E2B] p-1 text-center hover:bg-[#7f20ef]/30">
                                <span>+5 {" "}</span>
                                <span className="text-white">-110</span>
                              </button>
                            </div>

                            <div className="flex flex-col items-center p-0.5">
                              <button className="w-full rounded bg-[#171E2B] p-1 mb-1 text-center hover:bg-[#7f20ef]/30 text-[10px] sm:text-[11px]">
                                <span className="text-white">-192</span>
                              </button>
                              <button className="w-full rounded bg-[#171E2B] p-1 text-center hover:bg-[#7f20ef]/30 text-[10px] sm:text-[11px]">
                                <span className="text-white">+160</span>
                              </button>
                            </div>

                            <div className="flex flex-col items-center p-0.5">
                              <button className="w-full rounded bg-[#171E2B] p-1 mb-1 text-center hover:bg-[#7f20ef]/30 text-[10px] sm:text-[11px]">
                                <span>U24.5</span>
                                <span className="text-white">-112</span>
                              </button>
                              <button className="w-full rounded bg-[#171E2B] p-1 text-center hover:bg-[#7f20ef]/30 text-[10px] sm:text-[11px]">
                                <span>O24.5</span>
                                <span className="text-white">+108</span>
                              </button>
                            </div>
                          </div>

                          <div className="grid grid-cols-4 p-1.5 text-center text-[10px] sm:text-[11px]">
                            <span></span>
                            <span>77% DEN</span>
                            <span>79% DEN</span>
                            <span>75% UNDER</span>
                          </div>
                        </div>

                        <div
                          className="rounded-[12px] bg-[#3131314D] text-[#FFFFFFB2] cursor-pointer hover:bg-[#3131316D] transition-colors"
                        >
                          <div className="flex items-center justify-start p-2 gap-1.5">
                            <h2 className="text-[17px] text-white">DarkHorse Picks</h2>
                            <div className="flex items-center gap-1">
                              <span className="text-[12px] text-gray-400">AI Prediction</span>
                              <Image
                                src="/prediction-icon.svg"
                                alt="Prediction Icon"
                                width={15}
                                height={15}
                              />
                            </div>
                          </div>

                          <div className='p-2'>
                          <div className='flex flex-col mb-4'>
                          <div className="flex justify-between gap-2 mb-2 text-[12px]">
                            <div className="font-bold text-[#19FB9B]">77% {" "} <span className='text-white'>-4.5</span></div>
                            <div className="font-semibold">SPREAD</div>
                            <div className="text-white">+4.5 {" "} <span className='text-gray-400'>23%</span></div>
                          </div>
                          <div className="flex justify-between gap-2">
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 right-0 h-full w-[85%] gradient-progress" />
                            </div>
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 left-0 h-full w-[20%] normal-progress" />
                            </div>
                          </div>
                        </div>

                        <div className='flex flex-col mb-4'>
                          <div className="flex justify-between gap-2 mb-2 text-[12px]">
                            <div className="font-bold text-[#19FB9B]">79% {" "} <span className='text-white'>-190</span></div>
                            <div className="font-semibold">MONEYLINE</div>
                            <div className="text-white">+155 {" "} <span className='text-gray-400'>21%</span></div>
                          </div>
                          <div className="flex justify-between gap-2">
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 right-0 h-full w-[85%] gradient-progress" />
                            </div>
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 left-0 h-full w-[20%] normal-progress" />
                            </div>
                          </div>
                        </div>

                        <div className='flex flex-col mb-4'>
                          <div className="flex justify-between gap-2 mb-2 text-[12px]">
                            <div className="font-bold text-[#19FB9B]">75% {" "} <span className='text-white'>u246.0</span></div>
                            <div className="font-semibold">TOTAL O/U</div>
                            <div className="text-white">o246.0 {" "} <span className='text-gray-400'>25%</span></div>
                          </div>
                          <div className="flex justify-between gap-2">
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 right-0 h-full w-[85%] gradient-progress" />
                            </div>
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 left-0 h-full w-[20%] normal-progress" />
                            </div>
                          </div>
                        </div>
                          </div>

                          
                        </div>
                      </div>

                      <div className={`tab-content ${activeTab === 'game-metrics' ? 'active' : ''}`}>
                        <div className="flex justify-between items-center mb-6">
                          <h2 className="text-[17px] font-semibold">Game Metrics</h2>
                          <div className="flex items-center gap-1">
                            <span className="text-[12px] text-gray-400">DarkHorse AI Prediction</span>
                            <Image
                              src="/prediction-icon.svg"
                              alt="Prediction Icon"
                              width={15}
                              height={15}
                            />
                          </div>
                        </div>
                        <div className='flex flex-col mb-4'>
                          <div className="flex justify-between gap-2 mb-2 text-[12px]">
                            <div className="font-bold text-[#19FB9B]">28.5</div>
                            <div className="font-semibold">3-GAME AVG</div>
                            <div className="text-gray-400">13</div>
                          </div>
                          <div className="flex justify-between gap-2">
                            <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 right-0 h-full w-[85%] gradient-progress" />
                            </div>
                            <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 left-0 h-full w-[100%] normal-progress" />
                            </div>
                          </div>
                        </div>

                        <div className='flex flex-col mb-4'>
                          <div className="flex justify-between gap-2 mb-2 text-[12px]">
                            <div className="font-bold text-[#19FB9B]">245.5</div>
                            <div className="font-semibold">3-GAME HR</div>
                            <div className="text-gray-400">185.3</div>
                          </div>
                          <div className="flex justify-between gap-2">
                            <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 right-0 h-full w-[35%] gradient-progress" />
                            </div>
                            <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 left-0 h-full w-[70%] normal-progress" />
                            </div>
                          </div>
                        </div>

                        <div className='flex flex-col mb-4'>
                          <div className="flex justify-between gap-2 mb-2 text-[12px]">
                            <div className="font-bold text-[#19FB9B]">28.5</div>
                            <div className="font-semibold">3-GAME RPG</div>
                            <div className="text-gray-400">13</div>
                          </div>
                          <div className="flex justify-between gap-2">
                            <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 right-0 h-full w-[95%] gradient-progress" />
                            </div>
                            <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 left-0 h-full w-[10%] normal-progress" />
                            </div>
                          </div>
                        </div>

                        <div className='flex flex-col mb-4'>
                          <div className="flex justify-between gap-2 mb-2 text-[12px]">
                            <div className="font-bold text-[#19FB9B]">28.5</div>
                            <div className="font-semibold">3-GAME ERA</div>
                            <div className="text-gray-400">13</div>
                          </div>
                          <div className="flex justify-between gap-2">
                            <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 right-0 h-full w-[15%] gradient-progress" />
                            </div>
                            <div className="w-full h-1.5 rounded-full bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 left-0 h-full w-[90%] normal-progress" />
                            </div>
                          </div>
                        </div>

                        <div className='flex flex-col mb-4'>
                          <div className="flex justify-between gap-2 mb-2 text-[12px]">
                            <div className="font-bold text-[#19FB9B]">28.5</div>
                            <div className="font-semibold">3-GAME K/9</div>
                            <div className="text-gray-400">13</div>
                          </div>
                          <div className="flex justify-between gap-2">
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 right-0 h-full w-[45%] gradient-progress" />
                            </div>
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 left-0 h-full w-[40%] normal-progress" />
                            </div>
                          </div>
                        </div>

                        <div className='flex flex-col mb-4'>
                          <div className="flex justify-between gap-2 mb-2 text-[12px]">
                            <div className="font-bold text-[#19FB9B]">28.5</div>
                            <div className="font-semibold">3-GAME BB/9</div>
                            <div className="text-gray-400">13</div>
                          </div>
                          <div className="flex justify-between gap-2">
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 right-0 h-full w-[15%] gradient-progress" />
                            </div>
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 left-0 h-full w-[80%] normal-progress" />
                            </div>
                          </div>
                        </div>

                        <div className='flex flex-col mb-4'>
                          <div className="flex justify-between gap-2 mb-2 text-[12px]">
                            <div className="font-bold text-[#19FB9B]">28.5</div>
                            <div className="font-semibold">3-GAME FPCT</div>
                            <div className="text-gray-400">13</div>
                          </div>
                          <div className="flex justify-between gap-2">
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 right-0 h-full w-[75%] gradient-progress" />
                            </div>
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 left-0 h-full w-[20%] normal-progress" />
                            </div>
                          </div>
                        </div>

                        <div className='flex flex-col mb-4'>
                          <div className="flex justify-between gap-2 mb-2 text-[12px]">
                            <div className="font-bold text-[#19FB9B]">28.5</div>
                            <div className="font-semibold">3-GAME E</div>
                            <div className="text-gray-400">13</div>
                          </div>
                          <div className="flex justify-between gap-2">
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 right-0 h-full w-[55%] gradient-progress" />
                            </div>
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 left-0 h-full w-[50%] normal-progress" />
                            </div>
                          </div>
                        </div>

                        <div className='flex flex-col mb-4'>
                          <div className="flex justify-between gap-2 mb-2 text-[12px]">
                            <div className="font-bold text-[#19FB9B]">28.5</div>
                            <div className="font-semibold">3-GAME OBP</div>
                            <div className="text-gray-400">13</div>
                          </div>
                          <div className="flex justify-between gap-2">
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 right-0 h-full w-[15%] gradient-progress" />
                            </div>
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 left-0 h-full w-[80%] normal-progress" />
                            </div>
                          </div>
                        </div>

                        <div className='flex flex-col mb-4'>
                          <div className="flex justify-between gap-2 mb-2 text-[12px]">
                            <div className="font-bold text-[#19FB9B]">28.5</div>
                            <div className="font-semibold">3-GAME DSPO</div>
                            <div className="text-gray-400">13</div>
                          </div>
                          <div className="flex justify-between gap-2">
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 right-0 h-full w-[85%] gradient-progress" />
                            </div>
                            <div className="w-full h-1.5 rounded-[80px] bg-[#00000038] relative overflow-hidden">
                              <div className="absolute top-0 left-0 h-full w-[20%] normal-progress" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
              </ScrollArea>

                </div>
            </main>
            <aside className="hidden xl:block w-[280px] xl:w-[320px] 2xl:w-[320px] h-full flex-shrink-0 bg-[#08101e] p-4">
              <RightAsideContent
                currentView={currentView}
                onViewChange={setCurrentView}
              />
            </aside>
            <FloatingActionButton />
          </div>
        </div>
      </div >
    </div >
  )
}




























