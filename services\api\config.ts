export const API_BASE_URL = 'https://api.darkhorsewin.com';

export const API_ENDPOINTS = {
  users: {
    create: '/users/',
    getById: (id: string) => `/users/${id}`,
    update: (id: string) => `/users/${id}`,
    delete: (id: string) => `/users/${id}`,
    getByEmail: '/users/get_user_id/',
  },
  nfl: {
    predictions: {
      upload: '/nfl/predictions/upload/',
      getByWeek: (weekNumber: number) => `/nfl/predictions/week/${weekNumber}`,
      getByGameIds: '/nfl/predictions/batch/'
    },
    trainModel: '/nfl/train_model',
    trainRlAgent: '/nfl/train_rl_agent',
    predictUpcomingGames: '/nfl/predict_upcoming_games',
    pick6: '/nfl/pick6'
  },
  nba: {
    predictions: {
      upload: '/nba/predictions/upload/',
      getByWeek: (weekNumber: number) => `/nba/predictions/week/${weekNumber}`,
      getByGameIds: '/nba/predictions/batch/'
    },
    trainModel: '/nba/train_model',
    trainRlAgent: '/nba/train_rl_agent',
    predictUpcomingGames: '/nba/predict_upcoming_games',
    pick6: '/nba/pick6'
  }
} as const;
