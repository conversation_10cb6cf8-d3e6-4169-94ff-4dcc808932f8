"use client"

import Image from "next/image"
import Link from "next/link"
import { Eye, EyeOff } from "lucide-react"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { signIn, signInWithRedirect, getCurrentUser } from "aws-amplify/auth"
import { Hub } from "aws-amplify/utils"
import { toast } from "sonner"

export default function LoginPage() {
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [emailError, setEmailError] = useState<string | null>(null)
  const [passwordError, setPasswordError] = useState<string | null>(null)
  const [isFormFilled, setIsFormFilled] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [customState, setCustomState] = useState<any>(null)

  useEffect(() => {
    const unsubscribe = Hub.listen("auth", ({ payload }) => {
      switch (payload.event) {
        case "signInWithRedirect":
          getUser()
          break
        case "signInWithRedirect_failure":
          setError("An error has occurred during the OAuth flow")
          toast.error("Failed to sign in with Google")
          break
        case "customOAuthState":
          setCustomState(payload.data)
          break
      }
    })

    return unsubscribe
  }, [])

  const getUser = async () => {
    try {
      const currentUser = await getCurrentUser()
      setUser(currentUser)
      toast.success('Login successful!', {
        duration: 3000,
        style: {
          backgroundColor: 'linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)',
          borderWidth: '1px',
          borderStyle: 'solid',
          borderColor: 'rgba(25, 251, 155, 0.2)',
          backdropFilter: 'blur(8px)',
        },
      })
      router.push('/main')
    } catch (error) {
      console.error(error)
      console.log("Not signed in")
    }
  }

  useEffect(() => {
    setIsFormFilled(email.length > 0 && password.length > 0)
  }, [email, password])

  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!isFormFilled || isLoading) return

    setIsLoading(true)
    setEmailError(null)
    setPasswordError(null)
    try {
      const { isSignedIn, nextStep } = await signIn({
        username: email,
        password,
      })

      if (isSignedIn) {
        toast.success('Login successful!', {
          duration: 3000,
          style: {
            background: 'linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)',
            border: '1px solid rgba(25, 251, 155, 0.2)',
            backdropFilter: 'blur(8px)',
          },
        })
        router.push('/main')
      } else if (nextStep.signInStep === 'CONFIRM_SIGN_UP') {
        router.push(`/verify-email?email=${encodeURIComponent(email)}`)
      }
    } catch (error: any) {
      console.error('Login Error:', error)

      if (error.name === 'UserNotFoundException') {
        setEmailError('No account found with this email')
      } else if (error.name === 'NotAuthorizedException') {
        setPasswordError('Incorrect password')
      } else if (error.name === 'UserNotConfirmedException') {
        router.push(`/verify-email?email=${encodeURIComponent(email)}`)
      } else {
        setPasswordError('An error occurred during sign in')
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen md:max-h-screen bg-[#050816] p-[20px] gap-[40px] relative overflow-hidden">
      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "10px",
          left: "0%",
          background: "#2CAD9C",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />

      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "450px",
          right: "0%",
          background: "#7F20EF",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />
      <div className="w-full lg:w-1/2 flex items-center justify-center relative z-10">
        <div className="w-full max-w-[540px] flex flex-col items-center">
          <Link href="/" className="flex items-center">
            <div
              className="flex items-center rounded-[100px] w-[188px] h-[40px] px-6 relative mb-8 md:mb-8"
              style={{
                gap: "5px",
                background:
                  "linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)",
              }}
            >
              <div
                style={{
                  position: "absolute",
                  top: 0,
                  right: 0,
                  bottom: 0,
                  left: 0,
                  borderRadius: "100px",
                  padding: "1.5px",
                  background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                  WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                  WebkitMaskComposite: "xor",
                  maskComposite: "exclude",
                  pointerEvents: "none",
                }}
              />
              <Image src="https://borisbelov.com/dev/darkhorsewin/darkhorselogo.svg" alt="DarkHorse Icon" width={24} height={26} />
              <Image src="/DARKHORSE.svg" alt="DarkHorse" width={120} height={20} />
            </div>
          </Link>

          <div className="space-y-8">
            <div className="text-center">
              <h1 className="text-[40px] font-bold text-white mb-2">Log in</h1>
              <p className="text-[#9ca3af] text-base md:text-[24px]">We'll send you a code you can use to log in</p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-medium text-white">
                  Email
                </label>
                <div className="relative">
                  <input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => {
                      setEmail(e.target.value)
                      setEmailError(null)
                    }}
                    className={`w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border ${emailError ? 'border-red-500' : 'border-[#282828]'
                      } text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]`}
                  />
                  {emailError && (
                    <div className="flex items-center mt-1">
                      <p className="text-sm text-red-500">{emailError}</p>
                      <Link href="/signup" className="ml-2 text-sm text-[#7f20ef] hover:underline">
                        Sign up instead
                      </Link>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="block text-sm font-medium text-white">
                  Password
                </label>
                <div>
                  <div className="relative">
                    <input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="At least 8 characters"
                      value={password}
                      onChange={(e) => {
                        setPassword(e.target.value)
                        setPasswordError(null)
                      }}
                      className={`w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border ${passwordError ? 'border-red-500' : 'border-[#282828]'
                        } text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]`}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
                    >
                      {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                  {passwordError && (
                    <p className="text-sm text-red-500 mt-1">{passwordError}</p>
                  )}
                </div>
                <div className="mt-1">
                  <Link href="/forgot-password" className="text-sm text-[#9ca3af] hover:text-[#7f20ef]">
                    Forgot Password?
                  </Link>
                </div>
              </div>

              <button
                onClick={handleSubmit}
                disabled={!isFormFilled || isLoading}
                className={`w-full h-12 rounded-[70px] backdrop-blur-[20px] transition-all duration-300 flex items-center justify-center gap-[15.16px] ${isFormFilled && !isLoading
                  ? "bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white"
                  : "bg-[#********] text-[#9ca3af]"
                  }`}
              >
                {isLoading ? "Signing in..." : "Sign in"}
              </button>

              <div className="relative flex items-center justify-center my-2">
                <div className="flex-grow">
                  <div
                    style={{
                      height: '1px',
                      background: 'linear-gradient(267.78deg, #19FB9B 1.87%, #8C01FA 98.13%)',
                    }}
                  />
                </div>
                <div className="bg-transparent px-4 text-[#9ca3af] text-sm">Or</div>
                <div className="flex-grow">
                  <div
                    style={{
                      height: '1px',
                      background: 'linear-gradient(267.78deg, #8C01FA 1.87%, #19FB9B 98.13%)',
                    }}
                  />
                </div>
              </div>

              <button
                onClick={() => signInWithRedirect({ provider: 'Google' })}
                className="w-full h-12 rounded-[70px] bg-[#********] backdrop-blur-[20px] text-white hover:opacity-90 transition-opacity flex items-center justify-center gap-[15.16px]"
              >
                <Image
                  src="/Google.svg"
                  alt="Google Icon"
                  width={27}
                  height={27}
                />
                Sign in with Google
              </button>

              <button
                onClick={() => signInWithRedirect({ provider: 'Facebook' })}
                className="w-full h-12 rounded-[70px] bg-[#********] backdrop-blur-[20px] text-white hover:opacity-90 transition-opacity flex items-center justify-center gap-[15.16px]">
                <Image
                  src="/Facebook.svg"
                  alt="Facebook Icon"
                  width={27}
                  height={27}
                />
                Sign in with Facebook
              </button>

              <div className="text-center pt-4">
                <p className="text-white text-sm">
                  Don't you have an account?{" "}
                  <Link href="/signup" className="text-[#9ca3af] hover:underline">
                    Sign up
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="hidden lg:block w-1/2">
        <Image
          src="/login-image.webp"
          alt="Dark Horse App"
          width={800}
          height={900}
          className="w-full h-full"
          priority
        />
      </div>
    </div>
  )
}
