"use client"

import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area";
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { useIsMobile } from "@/hooks/use-mobile"
import { useRouter } from 'next/navigation'
import RightAsideContent from "@/components/right-aside-content"
import { useState, useEffect } from 'react'
import MainHeader from "@/components/main-header"
import LeftSidebar from "@/components/left-sidebar"
import FloatingActionButton from "@/components/floating-action-button"
import NFLIcon from "@/components/svg/nfl-team-icon"
import NBAIcon from "@/components/svg/nba-team-icon"
import MLBIcon from "@/components/svg/mlb-team-icon"
import MLSIcon from "@/components/svg/mls-team-icon"
import { OddsService, GameOdds } from '@/services/api/odds'
import { format, parseISO } from 'date-fns'

type League = 'all' | 'nfl' | 'nba' | 'mls' | 'mlb';

export default function MainPage() {
  const isMobile = useIsMobile();
  const [selectedSection, setSelectedSection] = useState<League>('all');
  const [currentView, setCurrentView] = useState<'slip' | 'profile' | 'notifications' | 'settings' | 'help-info' | 'contact' | 'sign-out'>('slip');
  const [oddsData, setOddsData] = useState<Record<League, GameOdds[]>>({
    all: [],
    nfl: [],
    nba: [],
    mls: [],
    mlb: []
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchOddsData = async () => {
      setIsLoading(true);
      try {
        const [nbaResponse, nflResponse] = await Promise.all([
          OddsService.getOddsByLeague('nba'),
          OddsService.getOddsByLeague('nfl')
        ]);

        const allGames = [
          ...(nbaResponse.data || []),
          ...(nflResponse.data || [])
        ];

        setOddsData({
          all: allGames,
          nba: nbaResponse.data || [],
          nfl: nflResponse.data || [],
          mls: [],
          mlb: []
        });
      } catch (error) {
        console.error('Error fetching odds data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOddsData();
  }, []);

  const handleSectionClick = (section: League) => {
    setSelectedSection(section);
    
    if (section === 'mls' || section === 'mlb') {
      console.log(`${section.toUpperCase()} data coming soon`);
    }
  };

  const handleProfileClick = () => {
    if (!isMobile) {
      setCurrentView('profile');
    }
  };

  return (
    <>
      <div className="relative">
        <div className="md:hidden">
          <div
            className="absolute opacity-30 blur-[150px] pointer-events-none"
            style={{
              width: "300px",
              height: "230px",
              top: "10px",
              left: "0%",
              background: "#2CAD9C",
              transform: "rotate(-164.56deg)",
              zIndex: "0",
            }}
          />

          <div
            className="absolute opacity-30 blur-[150px] pointer-events-none"
            style={{
              width: "300px",
              height: "230px",
              top: "450px",
              right: "60px",
              background: "#7F20EF",
              transform: "rotate(-164.56deg)",
              zIndex: "0",
            }}
          />
        </div>

        <div className="flex w-screen h-screen overflow-hidden bg-[#070f1c] text-white">
          <LeftSidebar />

          <div className="flex flex-1 flex-col h-screen">
            <MainHeader onProfileClick={handleProfileClick} />

            <div className="flex flex-1 h-[calc(100vh-64px)] overflow-hidden">
              <main className="flex-1 bg-[#050816] overflow-hidden">
                <div className="h-full px-3 md:px-4 pt-4">
                  <div className="flex flex-col gap-4 2xl:hidden mb-4">
                    <span className="font-inter font-bold text-[16px] md:text-[24px] leading-[130%] tracking-[-0.01em] text-left uppercase text-white">
                      Welcome back, Boris Belov
                    </span>

                    <div className="flex justify-between gap-4">
                      {[
                        { id: 'all', icon: '/icon-all', label: 'All' },
                        { id: 'nfl', icon: '/icon-american-football', label: 'NFL' },
                        { id: 'nba', icon: '/icon-basketball', label: 'NBA' },
                        { id: 'mls', icon: '/icon-football', label: 'MLS' },
                        { id: 'mlb', icon: '/icon-baseball', label: 'MLB' }
                      ].map((item) => (
                        <button
                          key={item.id}
                          className={`flex flex-col min-h-[66px] w-[60px] items-center justify-center rounded-[8px] bg-[#3131314D] p-2 relative ${selectedSection === item.id ? '' : 'bg-[#3131314D]'
                            }`}
                          onClick={() => handleSectionClick(item.id as League)}
                          style={
                            selectedSection === item.id
                              ? {
                                background:
                                  'linear-gradient(0deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4))',
                                position: 'relative',
                              }
                              : {}
                          }
                        >
                          {selectedSection === item.id && (
                            <div
                              style={{
                                position: 'absolute',
                                top: 0,
                                right: 0,
                                bottom: 0,
                                left: 0,
                                borderRadius: '8px',
                                padding: '1.5px',
                                background:
                                  'linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)',
                                WebkitMask:
                                  'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                                WebkitMaskComposite: 'xor',
                                maskComposite: 'exclude',
                                pointerEvents: 'none',
                              }}
                            />
                          )}
                          {item.id === 'nfl' && (
                            <NFLIcon
                              teamName={`${item.id}${selectedSection === item.id ? '' : '-grey'}`}
                              className="w-12 h-10"
                            />
                          )}
                          {item.id === 'nba' && (
                            <NBAIcon
                              teamName={`${item.id}${selectedSection === item.id ? '' : '-grey'}`}
                              className="w-12 h-10"
                            />
                          )}
                          {item.id === 'mls' && (
                            <MLSIcon
                              teamName={`${item.id}${selectedSection === item.id ? '' : '-grey'}`}
                              className="w-12 h-10"
                            />
                          )}
                          {item.id === 'mlb' && (
                            <MLBIcon
                              teamName={`${item.id}${selectedSection === item.id ? '' : '-grey'}`}
                              className="w-12 h-10"
                            />
                          )}
                          {item.id === 'all' && (
                            <div className="w-12 h-10 flex items-center justify-center">
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 6H20M4 12H20M4 18H20" stroke={selectedSection === item.id ? "#FFFFFF" : "#9CA2B5"} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                              </svg>
                            </div>
                          )}
                          <span
                            className={`text-xs ${selectedSection === item.id
                              ? 'text-[#2cad9c]'
                              : 'text-[#9CA2B5]'
                              }`}
                          >
                            {item.label}
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>

                  <div
                    className="relative mb-4 h-[230px] md:mb-6 md:h-[280px] overflow-hidden p-8 text-center"
                  >
                    <div className="absolute inset-0 w-full h-full rounded-[12px]" style={{
                      background: "linear-gradient(0deg, rgba(11, 10, 10, 0.4), rgba(0, 0, 0, 0.4)), linear-gradient(261.81deg, rgba(25, 251, 155, 0.4) -8.01%, rgba(140, 1, 250, 0.4) 100%)"
                    }}>
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          right: 0,
                          bottom: 0,
                          left: 0,
                          borderRadius: "12px",
                          padding: "1.5px",
                          background: "linear-gradient(261.81deg, #19FB9B 0%, #8C01FA 100%)",
                          WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                          WebkitMaskComposite: "xor",
                          maskComposite: "exclude",
                          pointerEvents: "none",
                        }}
                      />
                      <div className="absolute inset-0 w-full h-full bg-[url('/horse-vector.svg')] bg-no-repeat bg-center bg-cover" />
                    </div>
                    <div className={`relative z-10 space-y-2 md:space-y-4 text-center`}>
                      <div
                        className="mx-auto mb-2 w-fit flex items-center rounded-[100px] px-4 py-2 text-xs font-semibold uppercase tracking-wider relative"
                        style={{
                          background: "linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)"
                        }}
                      >
                        <div
                          style={{
                            position: "absolute",
                            top: 0,
                            right: 0,
                            bottom: 0,
                            left: 0,
                            borderRadius: "100px",
                            padding: "1.5px",
                            background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                            WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                            WebkitMaskComposite: "xor",
                            maskComposite: "exclude",
                            pointerEvents: "none",
                          }}
                        />
                        <span className="font-inter italic">Premium</span>
                      </div>
                      <h2 className="text-2xl md:text-3xl font-bold leading-tight mb-2 md:mb-1">
                        Unlock the future <br /> of sports predictions
                      </h2>
                      <p className="text-sm md:text-lg mb-3 md:mb-4 text-white/80">
                        AI-Powered insights to outsmart the game
                      </p>
                      <Button
                        className="font-bold text-[#070F1C] hover:bg-white/90 bg-white rounded-[78px] px-6 py-1.5 text-sm md:px-8 md:py-2 md:text-base"
                      >
                        Upgrade Now
                      </Button>
                    </div>
                  </div>

                  <Tabs defaultValue="all" className="h-full">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3 mb-4">
                      <div className="hidden md:flex items-center gap-4 min-w-fit">
                        <h2 className="text-[17px] font-medium whitespace-nowrap">This Week's Games</h2>
                        <div className="hidden xl:flex items-center gap-2 text-sm whitespace-nowrap">
                          <span className="text-[#9ca2b5]">Use credits</span>
                          <div className="flex h-5 w-5 items-center justify-center rounded-full bg-[#ffd900]">
                            <Image
                              src="/star-second.svg"
                              alt="Star Second Icon"
                              width={16}
                              height={16}
                            />
                          </div>
                          <span className="text-[#9ca2b5]">to purchase games</span>
                        </div>
                      </div>

                      <div className="flex w-full md:w-auto justify-between md:justify-end items-center">
                        <h2 className="text-[15px] font-medium md:hidden">Games</h2>
                        <TabsList className="flex items-center gap-1.5 bg-transparent">
                          <TabsTrigger
                            value="all"
                            className="rounded-[8px] bg-[#3131314D] px-1.5 py-1.5 md:px-2 md:py-2 text-xs md:text-sm data-[state=active]:bg-[#FFFFFF] data-[state=active]:text-black whitespace-nowrap"
                          >
                            All
                          </TabsTrigger>
                          <TabsTrigger
                            value="today"
                            className="rounded-[8px] bg-[#3131314D] px-1.5 py-1.5 md:px-2 md:py-2 text-xs md:text-sm data-[state=active]:bg-[#FFFFFF] data-[state=active]:text-black whitespace-nowrap"
                          >
                            Today
                          </TabsTrigger>
                          <TabsTrigger
                            value="tomorrow"
                            className="rounded-[8px] bg-[#3131314D] px-1.5 py-1.5 md:px-2 md:py-2 text-xs md:text-sm data-[state=active]:bg-[#FFFFFF] data-[state=active]:text-black whitespace-nowrap"
                          >
                            Tomorrow
                          </TabsTrigger>
                        </TabsList>
                      </div>
                    </div>

                    <ScrollArea
                      className="h-[calc(100vh-380px)]"
                    >
                      <style jsx global>{`
                        .scrollbar-thumb {
                          background: linear-gradient(38.98deg, #19FB9B -34.05%, #8C01FA 77.64%) !important;
                          width: 2px !important;
                          border-radius: 88px !important;
                        }
                      `}</style>
                      <TabsContent value="all" className="mt-0">
                        <div className="grid gap-4 pb-36 md:pb-24 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 3xl:grid-cols-3">
                          {isLoading ? (
                            <div>Loading games...</div>
                          ) : oddsData[selectedSection].length > 0 ? (
                            oddsData[selectedSection].map((game, index) => (
                              <GameCard key={index} game={game} />
                            ))
                          ) : (
                            <div>No games available for this league</div>
                          )}
                        </div>
                      </TabsContent>

                      <TabsContent value="today" className="mt-0">
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-1 lg:grid-cols-2 3xl:grid-cols-3 pb-6">
                          {[1, 2, 3].map((game) => (
                            <GameCard key={game} game={oddsData.all[game - 1] || {
                              kickoff_et: new Date().toISOString(),
                              home_team: "Team Home",
                              away_team: "Team Away",
                              ml_home: -110,
                              ml_away: -110,
                              spread_home: -1.5,
                              spread_odds_home: -110,
                              spread_away: 1.5,
                              spread_odds_away: -110,
                              total_pts: 220.5,
                              total_over: -110,
                              total_under: -110
                            }} />
                          ))}
                        </div>
                      </TabsContent>
                    </ScrollArea>
                  </Tabs>
                </div>
              </main>

              <aside className="hidden xl:block w-[280px] xl:w-[320px] 2xl:w-[320px] h-full flex-shrink-0 bg-[#08101e] p-4">
                <RightAsideContent
                  currentView={currentView}
                  onViewChange={setCurrentView}
                />
              </aside>
            </div>
          </div>
        </div>
      </div>

      <FloatingActionButton />
    </>
  )
}

function GameCard({ game }: { game: GameOdds }) {
  const router = useRouter();
  const [selectedButton, setSelectedButton] = useState<string | null>(null);

  const isSelected = (buttonId: string) => selectedButton === buttonId;

  const handleButtonClick = (buttonId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedButton(selectedButton === buttonId ? null : buttonId);
  };

  const handleCardClick = () => {
    router.push(`/game/${game.away_team.replace(/\s+/g, '-').toLowerCase()}-vs-${game.home_team.replace(/\s+/g, '-').toLowerCase()}`);
  };

  // Format the date and time
  const gameDate = parseISO(game.kickoff_et);
  const formattedDate = format(gameDate, 'MMM. d, yyyy');
  const formattedTime = format(gameDate, 'h:mma');
  const todayTime = format(gameDate, 'h:mma');

  // Get team abbreviations (this is simplified - you might need a mapping function)
  const homeTeamAbbr = game.home_team.split(' ').pop()?.substring(0, 3).toUpperCase() || '';
  const awayTeamAbbr = game.away_team.split(' ').pop()?.substring(0, 3).toUpperCase() || '';

  return (
    <div
      className="overflow-hidden min-w-[320px] rounded-[12px] bg-[#3131314D] text-xs text-[#FFFFFFB2] cursor-pointer hover:bg-[#3131316D] transition-colors"
      onClick={handleCardClick}
    >
      <div className="flex items-center justify-between p-3">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2 bg-[#171E2B] rounded-[50px] px-3 py-2">
            <Image
              src="/notification.svg"
              alt="Notification Icon"
              width={16}
              height={16}
              style={{ width: 'auto', height: 'auto' }}
            />
            <span>TODAY {todayTime}</span>
          </div>
        </div>
        <button className="bg-[#171E2B] rounded-full p-2">
          <Image
            src="/star.svg"
            alt="Star Icon"
            width={16}
            height={16}
            style={{ width: 'auto', height: 'auto' }}
          />
        </button>
      </div>

      <div className="grid grid-cols-3 p-3">
        <div className="col-span-1 flex items-center gap-1 overflow-hidden">
          <div className="min-w-0 flex-shrink">
            <div>{awayTeamAbbr}</div>
            <div className="text-white text-[14px] py-1 font-bold truncate md:break-words max-w-full leading-tight">
              {game.away_team.split(' ').pop()}
            </div>
            <div>0-0</div>
          </div>
          <NBAIcon teamName={game.away_team} className="w-10 h-10 md:w-12 md:h-12 flex-shrink-0" />
        </div>

        <div className="col-span-1 flex flex-col items-center justify-center">
          <div>{formattedDate}</div>
          <div className="text-white text-[14px] py-1 font-bold">{formattedTime}</div>
          <div className="text-center">{homeTeamAbbr} {game.spread_home > 0 ? '+' : ''}{game.spread_home} | O/U {game.total_pts}</div>
        </div>

        <div className="col-span-1 flex items-center justify-end gap-2">
          <NBAIcon teamName={game.home_team} className="w-10 h-10 md:w-12 md:h-12 flex-shrink-0" />
          <div className="text-right min-w-0 flex-shrink">
            <div className="text-xs">{homeTeamAbbr}</div>
            <div className="text-white text-[14px] py-1 font-bold truncate md:break-words max-w-full leading-tight">
              {game.home_team.split(' ').pop()}
            </div>
            <div className="text-xs">0-0</div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-4 p-2 text-center text-xs">
        <div></div>
        <div>SPREAD</div>
        <div>MONEYLINE</div>
        <div>TOTAL</div>
      </div>

      <div className="grid grid-cols-4 p-2">
        <div className="flex flex-col items-center p-1 text-white">
          <div className="mb-3 py-1 md:p-1">{awayTeamAbbr} {game.away_team.split(' ').pop()}</div>
          <div>{homeTeamAbbr} {game.home_team.split(' ').pop()}</div>
        </div>

        <div className="flex flex-col items-center p-1">
          <button
            onClick={(e) => handleButtonClick('spread-button', e)}
            className={`relative mb-1 w-full rounded-[4px] p-1 text-center text-[12px] md:text-sm ${isSelected('spread-button')
              ? 'bg-transparent'
              : 'bg-[#171E2B] hover:bg-[#28303f]'
              }`}
          >
            {isSelected('spread-button') && (
              <>
                <div
                  className="absolute inset-0 w-full h-full rounded-[4px] text-[12px] md:text-sm"
                  style={{
                    background: "linear-gradient(0deg, rgba(11, 10, 10, 0.4), rgba(0, 0, 0, 0.4)), linear-gradient(261.81deg, rgba(25, 251, 155, 0.4) -8.01%, rgba(140, 1, 250, 0.4) 100%)"
                  }}
                />
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 0,
                    borderRadius: "4px",
                    padding: "1.5px",
                    background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                    WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                    WebkitMaskComposite: "xor",
                    maskComposite: "exclude",
                    pointerEvents: "none",
                  }}
                />
              </>
            )}
            <span className="relative z-10">
              {game.spread_away > 0 ? '+' : ''}{game.spread_away} {" "}
            </span>
            <span className="relative z-10 text-white">
              {game.spread_odds_away}
            </span>
          </button>
          <button className="w-full rounded bg-[#171E2B] p-1 text-center hover:bg-[#7f20ef]/30">
            <span>{game.spread_home > 0 ? '+' : ''}{game.spread_home} {" "}</span>
            <span className="text-white">{game.spread_odds_home}</span>
          </button>
        </div>

        <div className="flex flex-col items-center p-1">
          <button className="w-full rounded bg-[#171E2B] p-1 mb-1 text-center hover:bg-[#7f20ef]/30">
            <span className="text-white">{game.ml_away}</span>
          </button>
          <button className="w-full rounded bg-[#171E2B] p-1 text-center hover:bg-[#7f20ef]/30">
            <span className="text-white">{game.ml_home}</span>
          </button>
        </div>

        <div className="flex flex-col items-center p-1">
          <button className="w-full rounded bg-[#171E2B] p-1 mb-1 text-center hover:bg-[#7f20ef]/30">
            <span>U{game.total_pts} {" "}</span>
            <span className="text-white">{game.total_under}</span>
          </button>
          <button className="w-full rounded bg-[#171E2B] p-1 text-center hover:bg-[#7f20ef]/30">
            <span>O{game.total_pts} {" "}</span>
            <span className="text-white">{game.total_over}</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-4 p-2 text-center">
        <span></span>
        <span className="">77% {awayTeamAbbr}</span>
        <span className="">79% {awayTeamAbbr}</span>
        <span className="">75% UNDER</span>
      </div>
    </div>
  );
}


