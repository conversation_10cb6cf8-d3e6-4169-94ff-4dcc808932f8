import { API_BASE_URL } from './config';
import type { ApiResponse } from '@/types/api';

export interface GameOdds {
  kickoff_et: string;
  home_team: string;
  away_team: string;
  ml_home: number;
  ml_away: number;
  spread_home: number;
  spread_odds_home: number;
  spread_away: number;
  spread_odds_away: number;
  total_pts: number;
  total_over: number;
  total_under: number;
}

export class OddsService {
  static async getOddsByLeague(league: 'nba' | 'nfl' | 'mlb' | 'mls', token?: string): Promise<ApiResponse<GameOdds[]>> {
    try {
      const isClient = typeof window !== 'undefined';
      const apiUrl = `${API_BASE_URL}/odds/${league}${token ? `?token=${encodeURIComponent(token)}` : ''}`;
      
      const url = isClient 
        ? `/api/cors-proxy?url=${encodeURIComponent(apiUrl)}`
        : apiUrl;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: `Failed to fetch ${league.toUpperCase()} odds`,
        },
      };
    }
  }
}
