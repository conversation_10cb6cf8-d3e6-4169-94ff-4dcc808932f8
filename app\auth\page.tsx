"use client";
import "aws-amplify/auth/enable-oauth-listener";

import { Amplify } from "aws-amplify";
import { useEffect } from "react";

import { amplifyConfig } from "../config/amplify-config";

export default function ConfigureAmplifyClientSide({
  children,
}: {
  children: React.ReactNode;
}) {
  useEffect(() => {
    Amplify.configure(amplifyConfig, { ssr: true });
  }, []);
  return <>{children}</>;
}