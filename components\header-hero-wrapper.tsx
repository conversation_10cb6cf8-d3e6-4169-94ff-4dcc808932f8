"use client"

import Navbar from "./navbar"
import Hero<PERSON><PERSON><PERSON> from "./hero-section"
import OurMission from "./our-mission"
import HowAiWorks from "./how-ai-works"
import HelpCenter from "./help-center"
import FAQAccordion from "./faq-accordion"
import { usePathname } from "next/navigation"
import GradientDivider from "./gradient-divider"

export default function HeaderHeroWrapper() {
  const pathname = usePathname()

  return (
    <div className="relative overflow-hidden">
      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "-100px",
          left: "0px",
          background: "#2CAD9C",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />

      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "450px", 
          right: "40px",
          background: "#7F20EF",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />

      <Navbar />
      {pathname === "/about" ? (
        <>
          <OurMission />
          <GradientDivider />
          <HowAiWorks />
        </>
      ) : pathname === "/support" ? (
        <>
          <HelpCenter />
          <GradientDivider />
          <FAQAccordion />
        </>
      ) : (
        <HeroSection />
      )}
    </div>
  )
}




