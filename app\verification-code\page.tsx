"use client"

import Image from "next/image"
import Link from "next/link"
import { useState, useEffect, Suspense } from "react"
import { useRouter } from "next/navigation"
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp"
import { confirmResetPassword, resetPassword } from "aws-amplify/auth"
import { toast } from "sonner"
import { useSearchParams } from "next/navigation"

function VerificationCodeForm() {
  const searchParams = useSearchParams()
  const email = searchParams.get('email') || ''
  const router = useRouter()
  const [verificationCode, setVerificationCode] = useState("")
  const [isFormFilled, setIsFormFilled] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    setIsFormFilled(verificationCode.length === 6)
  }, [verificationCode])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!isFormFilled || isLoading) return

    setIsLoading(true)
    setError(null)

    try {
      toast.success('Code verified successfully')
      router.push(`/reset-password?email=${encodeURIComponent(email)}&code=${encodeURIComponent(verificationCode)}`)
    } catch (error: any) {
      console.error('Verification Error:', error)
      setError(error.message)
      toast.error(error.message || 'Invalid verification code')
    } finally {
      setIsLoading(false)
    }
  }

  const handleResend = async () => {
    if (isLoading) return
    setIsLoading(true)
    setError(null)

    try {
      const output = await resetPassword({ username: email })
      const { nextStep } = output
      if (nextStep.resetPasswordStep === 'CONFIRM_RESET_PASSWORD_WITH_CODE') {
        const { deliveryMedium } = nextStep.codeDeliveryDetails
        toast.success(`New code sent to your ${deliveryMedium?.toLowerCase() ?? 'email'}`)
      }
    } catch (error: any) {
      console.error('Resend Code Error:', error)
      setError(error.message)
      toast.error(error.message || 'Failed to resend code')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen bg-[#050816] p-[30px] gap-[30px] relative overflow-hidden">
      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "10px",
          left: "0%",
          background: "#2CAD9C",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />

      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "450px",
          right: "0%",
          background: "#7F20EF",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />
      <div className="w-full lg:w-1/2 flex items-center justify-center relative z-10">
        <div className="w-full max-w-[520px] flex flex-col items-center">
          <Link href="/" className="flex items-center">
            <div
              className="flex items-center rounded-[100px] w-[188px] h-[40px] px-6 relative mb-16 lg:mb-8 mt-5 lg:mt-0"
              style={{
                gap: "5px",
                background:
                  "linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)",
              }}
            >
              <div
                style={{
                  position: "absolute",
                  top: 0,
                  right: 0,
                  bottom: 0,
                  left: 0,
                  borderRadius: "100px",
                  padding: "1.5px",
                  background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                  WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                  WebkitMaskComposite: "xor",
                  maskComposite: "exclude",
                  pointerEvents: "none",
                }}
              />
              <Image src="https://borisbelov.com/dev/darkhorsewin/darkhorselogo.svg" alt="DarkHorse Icon" width={24} height={26} />
              <Image src="/DARKHORSE.svg" alt="DarkHorse" width={120} height={20} />
            </div>
          </Link>

          <div className="space-y-8">
            <div className="text-center">
              <h1 className="text-[28px] mb:text-[40px] font-bold text-white mb-4">Verification Code</h1>
              <p className="text-[#9ca3af] text-base md:text-[24px]">Please enter the verification code that we sent to your email</p>
            </div>

            <div className="space-y-4">
              <form onSubmit={handleSubmit}>
                <div className="space-y-2">
                  <label htmlFor="verificationCode" className="block text-sm font-medium text-white">
                    Verification Code
                  </label>
                  <div className="relative flex justify-center">
                    <InputOTP
                      maxLength={6}
                      value={verificationCode}
                      onChange={setVerificationCode}
                    >
                      <InputOTPGroup className="gap-4 md:gap-8">
                        {Array.from({ length: 6 }).map((_, index) => (
                          <InputOTPSlot
                            key={index}
                            index={index}
                            className="!rounded-[10px] w-12 h-12 text-center text-white bg-[#28282847] backdrop-blur-[20px] border border-[#282828] focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]"
                          />
                        ))}
                      </InputOTPGroup>
                    </InputOTP>
                  </div>
                </div>

                <button
                  type="submit"
                  className={`w-full h-12 rounded-[70px] backdrop-blur-[20px] transition-all duration-300 flex items-center justify-center gap-[15.16px] mt-6 ${isFormFilled
                      ? "bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white"
                      : "bg-[#28282880] text-[#9ca3af]"
                    }`}
                >
                  Submit
                </button>
              </form>

              <div className="text-center pt-4">
                <p className="text-white text-sm">
                  Didn't receive the code?{" "}
                  <Link href="#" className="text-[#9ca3af] hover:underline" onClick={handleResend}>
                    Resend
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="hidden lg:block w-1/2">
        <Image
          src="/login-image.webp"
          alt="Dark Horse App"
          width={800}
          height={900}
          className="w-full h-auto"
          priority
        />
      </div>
    </div>
  )
}

export default function VerificationCodePage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center bg-[#050816]">
        <div className="text-white">Loading...</div>
      </div>
    }>
      <VerificationCodeForm />
    </Suspense>
  )
}







