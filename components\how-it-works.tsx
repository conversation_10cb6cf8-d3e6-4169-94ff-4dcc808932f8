import Image from "next/image"
import { Ch<PERSON>ron<PERSON>eft, ChevronDown, Star, Bell } from "lucide-react"
import NuggetsLogo from "./svg/nuggets-logo"
import PacersLogo from "./svg/pacers-logo"

export default function HowItWorks() {
  return (
    <section id="how-it-works" className="py-16 md:py-24 bg-[#050816] relative">
      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "60%",
          right: "50px",
          transform: "translateY(-50%) rotate(-164.56deg)",
          background: "#2CAD9C",
          zIndex: "0",
        }}
      />

      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "30%",
          left: "50px",
          transform: "translateY(-50%) rotate(-164.56deg)",
          background: "#7F20EF",
          zIndex: "0",
        }}
      />

      <div className="container mx-auto px-4 max-w-[1200px] relative">
        <h2 className="text-center text-4xl md:text-5xl font-bold mb-4">
          <span className="bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] bg-clip-text text-transparent">
            How It Works
          </span>
        </h2>

        <p className="text-center text-white text-[16px] sm:text-[18px] md:text-xl mb-16">AI-Powered Predictions Made Simple</p>

        <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
          <div className="w-full lg:w-1/2 text-center lg:text-left order-1 lg:order-2">
            <h3 className="text-white text-[18px] sm:text-[24px] md:text-3xl font-bold mb-4">Select a Game</h3>
            <p className="text-[#9ca2b5] text-[14px] sm:text-[16px] md:text-lg">Browse upcoming matchups & <br /> AI-generated insights.</p>
          </div>

          <div className="w-full lg:w-1/2 order-2 lg:order-1 relative">
            <Image
              src="/frame-first.webp"
              alt="Card Frame"
              width={620}
              height={431}
              className="w-full h-auto"
            />
          </div>
        </div>
      </div>
      <div className="container mx-auto px-4 mt-24 max-w-[1200px]">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
          <div className="w-full lg:w-1/2 text-center lg:text-right">
            <h3 className="text-white text-[18px] sm:text-[24px] md:text-3xl font-bold mb-4">Review Predictions</h3>
            <p className="text-[#9ca2b5] text-[14px] sm:text-[16px] md:text-lg">
              Compare moneyline odds, spreads,
              <br />
              and totals from our single-model AI.
            </p>
          </div>

          <div className="w-full lg:w-1/2 order-2 lg:order-1 relative">
            <Image
              src="/frame-second.svg"
              alt="Card Frame"
              width={620}
              height={431}
              className="w-full h-auto"
            />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 mt-24 max-w-[1200px]">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
          <div className="w-full lg:w-1/2 text-center lg:text-left order-1 lg:order-2">
            <h3 className="text-white text-[18px] sm:text-[24px] md:text-3xl font-bold mb-4">Leverage AI Confidence</h3>
            <p className="text-[#9ca2b5] text-[14px] sm:text-[16px] md:text-lg">See how strongly our system supports each pick,
              <br />
              reducing uncertainty in your decisions.</p>
          </div>
          <div className="w-full lg:w-1/2 order-2 lg:order-1 relative">
            <Image
              src="/frame-third.svg"
              alt="Card Frame"
              width={620}
              height={431}
              className="w-full h-auto"
            />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 mt-24 text-center max-w-[1200px]">
        <h2 className="text-white text-[18px] sm:text-2xl md:text-4xl font-bold mb-8">See the Data Behind Our Picks!</h2>
        <div className="flex flex-col gap-4 md:hidden w-full max-w-md mx-auto">
          <button className="bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white text-[16px] font-medium rounded-full w-full h-[56px] flex items-center justify-center hover:opacity-90 transition-opacity">
            Explore AI Insights
          </button>
        </div>
        <div className="hidden md:flex flex-row items-center justify-center gap-4">
          <button className="bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white text-[14px] font-medium rounded-[88px] w-[224px] h-[48px] flex items-center justify-center hover:opacity-90 transition-opacity">
            Explore AI Insights
          </button>
        </div>
      </div>
    </section>
  )
}












