export interface CreateUserRequest {
  full_name: string;
  email: string;
  preferred_sports?: string[];
  rag_credits?: number;
  referral_code?: string;
}

export interface User {
  user_id: string;
  full_name: string;
  email: string;
  created_at: string;
  preferred_sports: string[];
  rag_credits: number;
}

export interface ApiResponse<T> {
  data?: T;
  error?: {
    detail: string;
  };
}

export interface CreditProduct {
  id: string;
  name: string;
  description: string;
  amount: number;
  price: number;
  stripePriceId: string;
  type: 'first' | 'second';
}