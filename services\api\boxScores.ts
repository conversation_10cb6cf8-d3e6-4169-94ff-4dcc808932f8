import { API_BASE_URL } from './config';
import type { ApiResponse } from '@/types/api';

export class BoxScoresService {
  static async getNBABoxScores(token?: string): Promise<ApiResponse<any>> {
    try {
      const url = `${API_BASE_URL}/api/nba/box_scores${token ? `?token=${encodeURIComponent(token)}` : ''}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to fetch NBA box scores',
        },
      };
    }
  }

  static async getNFLBoxScores(token?: string): Promise<ApiResponse<any>> {
    try {
      const url = `${API_BASE_URL}/api/nfl/box_scores${token ? `?token=${encodeURIComponent(token)}` : ''}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to fetch NFL box scores',
        },
      };
    }
  }

  static async getMLBBoxScores(token?: string): Promise<ApiResponse<any>> {
    try {
      const url = `${API_BASE_URL}/api/mlb/box_scores${token ? `?token=${encodeURIComponent(token)}` : ''}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to fetch MLB box scores',
        },
      };
    }
  }

  static async getMLSBoxScores(token?: string): Promise<ApiResponse<any>> {
    try {
      const url = `${API_BASE_URL}/api/mls/box_scores${token ? `?token=${encodeURIComponent(token)}` : ''}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData };
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to fetch MLS box scores',
        },
      };
    }
  }

  static async getAllBoxScores(token?: string): Promise<ApiResponse<any>> {
    try {
      const [nbaResponse, nflResponse, mlbResponse, mlsResponse] = await Promise.all([
        this.getNBABoxScores(token),
        this.getNFLBoxScores(token),
        this.getMLBBoxScores(token),
        this.getMLSBoxScores(token)
      ]);

      // Combine all data with a sport identifier
      const allData = {
        nba: nbaResponse.data?.data || [],
        nfl: nflResponse.data?.data || [],
        mlb: mlbResponse.data?.data || [],
        mls: mlsResponse.data?.data || []
      };

      return { data: allData };
    } catch (error) {
      return {
        error: {
          detail: 'Failed to fetch all box scores',
        },
      };
    }
  }
}