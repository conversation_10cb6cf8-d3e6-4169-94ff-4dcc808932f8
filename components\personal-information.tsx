'use client'

import { useState, useEffect } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { ChevronLeft } from 'lucide-react'
import { toast } from "sonner"
import { getCurrentUser, updateUserAttributes, fetchUserAttributes } from "aws-amplify/auth"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"

interface PersonalInformationProps {
  onBack: () => void
}

export function PersonalInformation({ onBack }: PersonalInformationProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [userData, setUserData] = useState({
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    phone: '',
    avatarUrl: '/sample-avatar.svg'
  })
  const [showPasswordModal, setShowPasswordModal] = useState(false)
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  // Load user data on component mount
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const user = await getCurrentUser()
        const userAttributes = await fetchUserAttributes()
        
        const name = userAttributes.name || ''
        const nameParts = name.split(' ')
        const firstName = nameParts[0] || ''
        const lastName = nameParts.slice(1).join(' ') || ''
        
        setUserData({
          firstName,
          lastName,
          username: userAttributes.preferred_username || '',
          email: userAttributes.email || '',
          phone: userAttributes.phone_number || '',
          avatarUrl: '/sample-avatar.svg' // Default avatar
        })
      } catch (error) {
        console.error('Error fetching user data:', error)
        toast.error('Failed to load user information')
      }
    }
    
    fetchUserData()
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setUserData(prev => ({ ...prev, [name]: value }))
  }

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setPasswordData(prev => ({ ...prev, [name]: value }))
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      await updateUserAttributes({
        userAttributes: {
          name: `${userData.firstName} ${userData.lastName}`,
          preferred_username: userData.username,
          phone_number: userData.phone
        }
      })
      
      toast.success('Profile updated successfully', {
        duration: 3000,
        style: {
          background: 'linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)',
          border: '1px solid rgba(25, 251, 155, 0.2)',
          backdropFilter: 'blur(8px)',
        },
      })
    } catch (error) {
      console.error('Error updating profile:', error)
      toast.error('Failed to update profile')
    } finally {
      setIsLoading(false)
    }
  }

  const handleChangePassword = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('Passwords do not match')
      return
    }
    
    setIsLoading(true)
    try {
      // This is a simplified version - in a real app, you'd use Cognito's changePassword API
      // For demo purposes, we're just showing a success message
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
      
      setShowPasswordModal(false)
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
      
      toast.success('Password changed successfully')
    } catch (error) {
      console.error('Error changing password:', error)
      toast.error('Failed to change password')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full flex flex-col bg-[#08101e] text-white">
      <div className="flex w-full justify-between mb-3 sm:mb-4 gap-4">
        <h1 className="text-lg sm:text-xl font-medium">Personal Information</h1>
        <button 
          onClick={onBack}
          className="flex items-center text-[#bdbac0] hover:text-white transition-colors text-sm sm:text-base"
        >
          <ChevronLeft className="h-4 w-4 sm:h-5 sm:w-5 mr-1" />
          <span>Back</span>
        </button>
      </div>

      <div className="h-px bg-[#313038] w-full mb-4 sm:mb-6"></div>
      
      <div className="flex flex-col items-center mb-2">
        <Avatar className="h-20 w-20 mb-4 border-2 border-[#282828]">
          <AvatarImage src={userData.avatarUrl} alt={userData.firstName} />
          <AvatarFallback className="bg-[#181c23] text-lg">
            {userData.firstName.charAt(0)}{userData.lastName.charAt(0)}
          </AvatarFallback>
        </Avatar>
        {/* <Button
          variant="outline"
          className="rounded-full px-4 py-2 text-xs sm:text-sm border-[#A8A3AC] bg-transparent text-[#fcfcfd] hover:bg-[#181c23] hover:text-[#ffffff]"
        >
          Change Avatar
        </Button> */}
      </div>
      
      <div className="space-y-4 w-full">
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-2">
            <label htmlFor="firstName" className="block text-sm font-medium text-white">
              First Name
            </label>
            <input
              id="firstName"
              name="firstName"
              type="text"
              value={userData.firstName}
              onChange={handleInputChange}
              className="w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border border-[#282828] text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]"
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="lastName" className="block text-sm font-medium text-white">
              Last Name
            </label>
            <input
              id="lastName"
              name="lastName"
              type="text"
              value={userData.lastName}
              onChange={handleInputChange}
              className="w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border border-[#282828] text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]"
            />
          </div>
        </div>
        
        <div className="space-y-2">
          <label htmlFor="username" className="block text-sm font-medium text-white">
            Username
          </label>
          <input
            id="username"
            name="username"
            type="text"
            value={userData.username}
            onChange={handleInputChange}
            className="w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border border-[#282828] text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]"
          />
        </div>
        
        <div className="space-y-2">
          <label htmlFor="email" className="block text-sm font-medium text-white">
            Email
          </label>
          <input
            id="email"
            name="email"
            type="email"
            value={userData.email}
            onChange={handleInputChange}
            disabled
            className="w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border border-[#282828] text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef] opacity-70"
          />
          <p className="text-xs text-[#a8a3ac]">Email cannot be changed</p>
        </div>
        
        <div className="space-y-2">
          <label htmlFor="phone" className="block text-sm font-medium text-white">
            Phone Number
          </label>
          <input
            id="phone"
            name="phone"
            type="tel"
            value={userData.phone}
            onChange={handleInputChange}
            className="w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border border-[#282828] text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]"
          />
        </div>
        
        <div className="pt-4 flex flex-col sm:flex-row gap-3 justify-between">
          <Button
            variant="outline"
            onClick={() => setShowPasswordModal(true)}
            className="rounded-full px-4 py-2 text-sm border-[#A8A3AC] bg-transparent text-[#fcfcfd] hover:bg-[#181c23] hover:text-[#ffffff]"
          >
            Change Password
          </Button>
          
          <Button
            onClick={handleSave}
            disabled={isLoading}
            className="rounded-full px-6 py-2 text-sm bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white hover:opacity-90"
          >
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>
      
      <Dialog open={showPasswordModal} onOpenChange={setShowPasswordModal}>
        <DialogContent className="sm:max-w-[425px] bg-[#08101e] border-[#282828] text-white">
          <DialogHeader>
            <DialogTitle>Change Password</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="currentPassword" className="block text-sm font-medium text-white">
                Current Password
              </label>
              <input
                id="currentPassword"
                name="currentPassword"
                type="password"
                value={passwordData.currentPassword}
                onChange={handlePasswordChange}
                className="w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border border-[#282828] text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]"
              />
            </div>
            
            <div className="space-y-2">
              <label htmlFor="newPassword" className="block text-sm font-medium text-white">
                New Password
              </label>
              <input
                id="newPassword"
                name="newPassword"
                type="password"
                value={passwordData.newPassword}
                onChange={handlePasswordChange}
                className="w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border border-[#282828] text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]"
              />
            </div>
            
            <div className="space-y-2">
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-white">
                Confirm New Password
              </label>
              <input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                value={passwordData.confirmPassword}
                onChange={handlePasswordChange}
                className="w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border border-[#282828] text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowPasswordModal(false)}
              className="rounded-full px-4 py-2 text-sm border-[#A8A3AC] bg-transparent text-[#fcfcfd] hover:bg-[#181c23]"
            >
              Cancel
            </Button>
            <Button
              onClick={handleChangePassword}
              disabled={isLoading}
              className="rounded-full px-6 py-2 mb-4 text-sm bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white hover:opacity-90"
            >
              {isLoading ? 'Changing...' : 'Change Password'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

