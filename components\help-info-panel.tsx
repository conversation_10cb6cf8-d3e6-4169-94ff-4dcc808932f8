'use client'

import { ChevronLeft, ChevronDown } from 'lucide-react'
import { useState } from 'react'

interface HelpInfoPanelProps {
  onBack: () => void
}

function FAQItem({ 
  question, 
  answer 
}: { 
  question: string
  answer: string 
}) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className="w-full">
      <button 
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between p-3 rounded-[8px] bg-[#28282880] hover:bg-[#282828] transition-colors"
      >
        <span className="text-sm text-left px-2">{question}</span>
        <ChevronDown 
          className={`h-5 w-5 text-[#a8a3ac] transition-transform ${
            isOpen ? 'transform rotate-180' : ''
          }`} 
        />
      </button>
      {isOpen && (
        <div className="p-3 text-sm text-[#a8a3ac] bg-[#28282840] mt-1 rounded-[8px]">
          {answer}
        </div>
      )}
    </div>
  )
}

export function HelpInfoPanel({ onBack }: HelpInfoPanelProps) {
  return (
    <div className="w-full flex items-center bg-[#08101e] text-white flex-col">
      <div className="flex w-full justify-between mb-3 sm:mb-4 gap-4">
        <h1 className="text-lg sm:text-xl font-medium">Help & Info</h1>
        <button 
          onClick={onBack}
          className="flex items-center text-[#bdbac0] hover:text-white transition-colors text-sm sm:text-base"
        >
          <ChevronLeft className="h-4 w-4 sm:h-5 sm:w-5 mr-1" />
          <span>Back</span>
        </button>
      </div>

      <div className="h-px bg-[#313038] w-full mb-4 sm:mb-6"></div>

      <div className="space-y-2 w-full">
        <FAQItem 
          question="Which sports and leagues are covered in the predictions?" 
          answer="Our AI-powered predictions cover major leagues such as the NFL, NBA, MLB, and MLS, along with other sports supported by the API. Predictions include team matchups, historical performance, and key player insights."
        />
        <FAQItem 
          question="What types of game predictions are available?" 
          answer="DarkHorse offers a comprehensive range of predictions including moneyline (winner), point spreads, over/under totals, and player performance metrics. Each prediction comes with a confidence score based on our AI's analysis of historical and current data."
        />
        <FAQItem 
          question="How does the AI determine the predicted winner?" 
          answer="Our AI analyzes thousands of data points including team performance, player statistics, historical matchups, recent form, injuries, and even venue conditions. The algorithm weighs these factors differently based on their proven impact on outcomes, continuously learning and improving its accuracy with each game."
        />
        <FAQItem 
          question="Can I customize predictions based on my preferences?" 
          answer="Yes! Premium users can customize their prediction feed to focus on specific teams, leagues, or types of bets. You can also set notification preferences to receive alerts for high-confidence predictions or games involving your favorite teams."
        />
      </div>
    </div>
  )
}