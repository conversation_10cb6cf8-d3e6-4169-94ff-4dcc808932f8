export default function ShieldLogoGradient({ width = 60, height = 60 }: { width?: number; height?: number }) {
  return (
    <svg width={width} height={height} viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M30 3L54 12V27C54 39 45 51 30 57C15 51 6 39 6 27V12L30 3Z" fill="url(#gradient)" />
      <path d="M30 15L39 30H21L30 15Z" fill="#FFFFFF" />
      <defs>
        <linearGradient id="gradient" x1="6" y1="3" x2="54" y2="57" gradientUnits="userSpaceOnUse">
          <stop offset="0" stopColor="#8C01FA" />
          <stop offset="1" stopColor="#19FB9B" />
        </linearGradient>
      </defs>
    </svg>
  )
}

