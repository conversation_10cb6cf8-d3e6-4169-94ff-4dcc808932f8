export default function MLBIcon({
    className = "",
    teamName = "Baltimore Orioles"
}: {
    className?: string;
    teamName?: string;
}) {
    const teamColors: Record<string, { bg: string, fill: string }> = {
        "Baltimore Orioles": { bg: "#DF4601", fill: "#292929" },
        "Boston Red Sox": { bg: "#BD3039", fill: "#FFFFFF" },
        "New York Yankees": { bg: "#00285E", fill: "#FFFFFF" },
        "Tampa Bay Rays": { bg: "#8FBCE6", fill: "#002F65" },
        "Toronto Blue Jays": { bg: "#1D2D5C", fill: "#E8291C" },
        "Chicago White Sox": { bg: "#27251F", fill: "#C6CCD2" },
        "Cleveland Guardians": { bg: "#CE0E2D", fill: "#0E2542" },
        "Detroit Tigers": { bg: "#0C2340", fill: "#FA4616" },
        "Kansas City Royals": { bg: "#004687", fill: "#C09A5B" },
        "Minnesota Twins": { bg: "#041E42", fill: "#E31B23" },
        "Houston Astros": { bg: "#F65314", fill: "#13274F" },
        "Los Angeles Angels": { bg: "#BA0021", fill: "#13274F" },
        "Oakland Athletics": { bg: "#003831", fill: "#FFFFFF" },
        "Seattle Mariners": { bg: "#002C56", fill: "#005C5C" },
        "Texas Rangers": { bg: "#C0111F", fill: "#003278" },
        "Atlanta Braves": { bg: "#13274F", fill: "#CE1141" },
        "Miami Marlins": { bg: "#292929", fill: "#1A85C8" },
        "New York Mets": { bg: "#002D72", fill: "#FF5910" },
        "Philadelphia Phillies": { bg: "#E31B23", fill: "#FFFFFF" },
        "Washington Nationals": { bg: "#AB0003", fill: "#11225B" },
        "Chicago Cubs": { bg: "#005596", fill: "#E31B23" },
        "Cincinnati Reds": { bg: "#FFFFFF", fill: "#B81137" },
        "Milwaukee Brewers": { bg: "#00285E", fill: "#C6A566" },
        "Pittsburgh Pirates": { bg: "#27251F", fill: "#FFC82E" },
        "St. Louis Cardinals": { bg: "#C41E3A", fill: "#FFFFFF" },
        "Arizona Diamondbacks": { bg: "#9B2743", fill: "#3EC1CC" },
        "Colorado Rockies": { bg: "#33006F", fill: "#C6CCD2" },
        "Los Angeles Dodgers": { bg: "#005A9C", fill: "#FFFFFF" },
        "San Diego Padres": { bg: "#BDA267", fill: "#2F241D" },
        "San Francisco Giants": { bg: "#27251F", fill: "#FD5A1E" },
        "all": {bg: "transparent", fill: "#FFFFFF"},
        "all-grey": {bg: "transparent", fill: "#9CA2B5"},
        "mlb": {bg: "transparent", fill: "#FFFFFF"},
        "mlb-grey": {bg: "transparent", fill: "#9CA2B5"}
    };

    const colors = teamColors[teamName] || { bg: "#0080C6", fill: "#FFFFFF" };

    return (
        <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={`${className} rounded-full p-1`}
            style={{ backgroundColor: colors.bg }}
        >
            <path d="M19.0865 14.9174L17.965 14.5177L18.5522 12.8688C18.066 12.6826 17.5937 12.4618 17.1389 12.2083L16.3508 13.6009L15.3115 13.0137L16.1222 11.5776C15.5929 11.2076 15.0934 10.7968 14.628 10.349L13.4897 11.4776L12.6487 10.6309L13.802 9.48651C13.3978 9.01573 13.0305 8.5145 12.7034 7.98727L11.177 8.68383L10.6829 7.59925L12.127 6.93915C11.9217 6.51579 11.7443 6.07947 11.5959 5.63298L9.63664 6.08925L9.36643 4.928L11.2902 4.48014C11.2445 4.25228 11.2077 4.02272 11.1798 3.79199C9.1253 3.98064 7.20223 4.88468 5.74617 6.34637C4.28349 7.80219 3.37859 9.72549 3.18945 11.7805C3.51109 11.8179 3.83553 11.8801 4.16277 11.9544L4.8411 10.0419L5.96542 10.4421L5.30672 12.2957C5.77655 12.4654 6.24264 12.6697 6.70172 12.9146L7.58154 11.3584L8.61937 11.9451L7.72459 13.5284C8.24912 13.881 8.74567 14.2734 9.20982 14.7023L10.4398 13.4817L11.2799 14.3279L10.0457 15.5522C10.4613 16.0169 10.8339 16.5012 11.1611 17.001L12.7506 16.2745L13.2461 17.3591L11.7726 18.0337C11.9914 18.4605 12.1737 18.8915 12.3289 19.3258L14.2901 18.8691L14.5593 20.0303L12.6683 20.4702C12.7417 20.7909 12.7992 21.1112 12.8371 21.4281C14.893 21.2393 16.8175 20.3352 18.2754 18.8733C19.7364 17.4164 20.6392 15.4925 20.8261 13.4378C20.4423 13.3894 20.062 13.3173 19.6872 13.2218L19.0865 14.9174Z" fill={colors.fill} />
            <path d="M13.463 3.97422L13.7313 5.1364L12.6701 5.38277C12.7963 5.75116 12.9478 6.1172 13.1259 6.48185L14.5059 5.8526L15.0001 6.93718L13.7163 7.52389C13.9781 7.9264 14.2647 8.3219 14.5896 8.70477L15.6307 7.67021L16.4727 8.51684L15.411 9.56963C15.8045 9.94664 16.2244 10.295 16.6676 10.6121L17.3777 9.35459L18.4174 9.93988L17.6807 11.2484C18.0888 11.4761 18.5049 11.6715 18.9195 11.8328L19.3753 10.5584L20.4982 10.9581L20.0593 12.1909C20.3295 12.2549 20.5969 12.3077 20.8643 12.3419C20.8008 10.0859 19.8757 7.93993 18.2791 6.34487C16.6825 4.74726 14.5351 3.82124 12.2774 3.75684C12.2975 3.91345 12.3307 4.07286 12.3634 4.23134L13.463 3.97422ZM10.4691 20.983L10.1985 19.8217L11.2522 19.5768C11.1123 19.2064 10.9511 18.8444 10.7693 18.4927L9.4229 19.106L8.92923 18.02L10.1433 17.466C9.87552 17.0682 9.58095 16.6892 9.26161 16.3314L8.29858 17.2875L7.45616 16.4413L8.422 15.4839C8.0319 15.1243 7.61631 14.7934 7.17847 14.4937L6.55156 15.6045L5.51233 15.0183L6.15934 13.8715C5.76543 13.6625 5.35741 13.4813 4.93825 13.3292L4.55677 14.4007L3.43339 14.0001L3.79289 12.9921C3.58163 12.9456 3.3686 12.9076 3.1543 12.8781C3.22395 15.0552 4.08414 17.2131 5.74607 18.8751C7.33922 20.4729 9.48433 21.3991 11.7398 21.4631C11.7066 21.2131 11.662 20.9648 11.6061 20.7188L10.4691 20.983Z" fill={colors.fill} />
        </svg>
    )
}
