export default function NBAIcon({
    className = "",
    teamName = "Boston Celtics"
}: {
    className?: string;
    teamName?: string;
}) {
    const teamColors: Record<string, { bg: string, fill: string }> = {
        "Boston Celtics": { bg: "#008248", fill: "#FFFFFF" },
        "Brooklyn Nets": { bg: "#FFFFFF", fill: "#292929" },
        "New York Knicks": { bg: "#0056A7", fill: "#FD5A1E" },
        "Philadelphia 76ers": { bg: "#C8102E", fill: "#0056A7" },
        "Toronto Raptors": { bg: "#C8102E", fill: "#292929" },
        "Chicago Bulls": { bg: "#292929", fill: "#CE1141" },
        "Cleveland Cavaliers": { bg: "#98002E", fill: "#B4975A" },
        "Detroit Pistons": { bg: "#C8102E", fill: "#0056A7" },
        "Indiana Pacers": { bg: "#002D62", fill: "#FDBB30" },
        "Milwaukee Bucks": { bg: "#004812", fill: "#EEE1C6" },
        "Atlanta Hawks": { bg: "#C8102E", fill: "#FFFFFF" },
        "<PERSON> Hornets": { bg: "#1A2857", fill: "#00788C" },
        "Miami Heat": { bg: "#F89E1A", fill: "#98002E" },
        "Orlando Magic": { bg: "#C2CCD2", fill: "#0077C0" },
        "Washington Wizards": { bg: "#00285E", fill: "#E31837" },
        "Denver Nuggets": { bg: "#FFC627", fill: "#FFFFFF" },
        "Minnesota Timberwolves": { bg: "#78BE20", fill: "#236192" },
        "Oklahoma City Thunder": { bg: "#007BC4", fill: "#EF3340" },
        "Portland Trail Blazers": { bg: "#292929", fill: "#E03A3E" },
        "Utah Jazz": { bg: "#292929", fill: "#FFF21F" },
        "Golden State Warriors": { bg: "#FDB927", fill: "#1D428A" },
        "Los Angeles Clippers": { bg: "#0C2340", fill: "#CE0E2D" },
        "Los Angeles Lakers": { bg: "#31006F", fill: "#FDB927" },
        "Phoenix Suns": { bg: "#F89E1A", fill: "#391366" },
        "Sacramento Kings": { bg: "#5A2D81", fill: "#63727A" },
        "Dallas Mavericks": { bg: "#00538C", fill: "#B8C4CA" },
        "Houston Rockets": { bg: "#C4CED4", fill: "#CE1141" },
        "Memphis Grizzlies": { bg: "#5D76A9", fill: "#12173F" },
        "New Orleans Pelicans": { bg: "#C6A566", fill: "#0C2340" },
        "San Antonio Spurs": { bg: "#292929", fill: "#C4CED4" },
        "all": { bg: "transparent", fill: "#FFFFFF" },
        "all-grey": { bg: "transparent", fill: "#9CA2B5" },
        "nba": { bg: "transparent", fill: "#FFFFFF" },
        "nba-grey": { bg: "transparent", fill: "#9CA2B5" }
    };

    const colors = teamColors[teamName] || { bg: "#0080C6", fill: "#FFFFFF" };

    return (
        <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={`${className} rounded-full p-1`}
            style={{ backgroundColor: colors.bg }}
        >
            <path d="M7.51135 7.17706C6.96949 6.57318 6.65356 5.94093 6.54276 5.29192C8.07496 4.00889 10.0459 3.19922 12.196 3.19922C13.3062 3.19922 14.3649 3.41407 15.3443 3.78999C13.7043 5.43312 12.3328 7.37867 11.26 9.4788C9.67931 8.90722 8.36183 8.12524 7.51135 7.17706Z" fill={colors.fill} />
            <path d="M6.7391 7.87026C7.69106 8.93127 9.1061 9.79418 10.7967 10.4213C10.5865 10.8821 10.3935 11.3525 10.2117 11.8289C9.34352 11.6422 8.44372 11.536 7.52607 11.536C6.08144 11.536 4.67802 11.7811 3.38392 12.2316C3.3827 12.1723 3.375 12.1146 3.375 12.0548C3.375 9.77054 4.25521 7.69297 5.68485 6.1247C5.89578 6.7325 6.24589 7.3207 6.7391 7.87026Z" fill={colors.fill} />
            <path d="M20.884 10.1908C19.7809 10.3858 18.5589 10.4982 17.3849 10.4982C15.565 10.4982 13.8169 10.2398 12.2589 9.79608C13.3324 7.73338 14.7126 5.83499 16.3599 4.24739C18.6018 5.45312 20.3316 7.63487 20.884 10.1908Z" fill={colors.fill} />
            <path d="M3.47337 13.3C4.72518 12.8288 6.10293 12.5737 7.52607 12.5737C8.32386 12.5737 9.10732 12.6574 9.86295 12.8101C9.09583 15.1106 8.65195 17.5972 8.57614 20.1247C5.8774 18.9038 3.90469 16.3323 3.47337 13.3Z" fill={colors.fill} />
            <path d="M21.0863 12.0548C21.0863 14.7839 19.7708 17.2614 17.8182 18.8806C17.6113 17.5043 16.9908 16.1504 15.9802 15.0172C14.7782 13.6695 13.11 12.6602 11.221 12.0842C11.3968 11.6307 11.5888 11.1861 11.7915 10.7478C13.4795 11.2484 15.3807 11.536 17.3849 11.536C18.611 11.536 19.887 11.4188 21.0437 11.2156C21.0699 11.4922 21.0863 11.7716 21.0863 12.0548Z" fill={colors.fill} />
            <path d="M15.2054 15.7078C16.2263 16.8526 16.7794 18.1952 16.8503 19.5688C15.4973 20.4137 13.9054 20.9105 12.196 20.9105C11.2973 20.9105 10.4302 20.7737 9.61256 20.5228C9.65228 17.9392 10.086 15.3988 10.8746 13.0606C12.6009 13.5783 14.1193 14.49 15.2054 15.7078Z" fill={colors.fill} />
        </svg>
    )
}
