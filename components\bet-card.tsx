"use client"

import { X, Minus, Plus } from "lucide-react"

interface BetCardProps {
  bet: {
    id: number
    team: string
    opponent: string
    spread: number
    odds: number
    amount: number
  }
  onRemove: () => void
  onUpdateAmount: (amount: number) => void
}

export default function BetCard({ bet, onRemove, onUpdateAmount }: BetCardProps) {
  const handleIncrement = () => {
    onUpdateAmount(bet.amount + 10)
  }

  const handleDecrement = () => {
    if (bet.amount > 10) {
      onUpdateAmount(bet.amount - 10)
    }
  }

  return (
    <div className="bg-[#171e2b] rounded-[8px] p-4">
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center gap-2">
          <button onClick={onRemove} className="bg-[#2B3955] rounded-full p-1 hover:bg-opacity-80 transition-colors">
            <X size={12} className="text-gray-400" />
          </button>
          <h2 className="text-[12px] md:text-[14px] font-bold">
            {bet.team} {bet.spread}
          </h2>
        </div>
        <span className="text-[12px] md:text-[14px] font-semibold">{bet.odds}</span>
      </div>

      <div className="flex justify-between items-center">
        <div>
          <p className="text-gray-400 text-[12px]">Spread</p>
          <p className="text-[10px]">
            {bet.team} @ {bet.opponent}
          </p>
        </div>

        <div className="flex items-center bg-[#2B3955] rounded-[8px] gap-2 p-1">
          <span className="text-[12px] font-semibold">${bet.amount.toFixed(2)}</span>

          <button onClick={handleDecrement} className="p-1 bg-[#171E2B] rounded-[6px] hover:bg-opacity-80 transition-colors">
            <Minus size={12} />
          </button>

          <button onClick={handleIncrement} className="p-1 bg-[#171E2B] rounded-[6px] hover:bg-opacity-80 transition-colors">
            <Plus size={12} />
          </button>
        </div>
      </div>
    </div>
  )
}