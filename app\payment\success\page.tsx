"use client"

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { CheckCircle } from 'lucide-react'

export default function PaymentSuccessPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const sessionId = searchParams.get('session_id')
  const [isLoading, setIsLoading] = useState(true)
  const [paymentDetails, setPaymentDetails] = useState<any>(null)

  useEffect(() => {
    if (sessionId) {
      // Verify the payment session and get details
      // This is optional - you could just show a success message
      fetch(`/api/verify-payment?session_id=${sessionId}`)
        .then(res => res.json())
        .then(data => {
          setPaymentDetails(data)
          setIsLoading(false)
        })
        .catch(err => {
          console.error('Error verifying payment:', err)
          setIsLoading(false)
        })
    } else {
      setIsLoading(false)
    }
  }, [sessionId])

  return (
    <div className="flex min-h-screen bg-[#050816] items-center justify-center p-4">
      <div className="w-full max-w-md bg-[#08101e] rounded-lg p-8 text-white">
        <div className="flex flex-col items-center text-center">
          <div className="mb-6 text-green-500">
            <CheckCircle size={64} />
          </div>
          
          <h1 className="text-2xl font-bold mb-2">Payment Successful!</h1>
          <p className="text-gray-400 mb-6">
            Your credits have been added to your account.
          </p>
          
          {!isLoading && paymentDetails && (
            <div className="w-full bg-[#FFFFFF0F] p-4 rounded-lg mb-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-400">Amount:</span>
                <span className="font-medium">${(paymentDetails.amount / 100).toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Credits:</span>
                <span className="font-medium">{paymentDetails.credits} {paymentDetails.creditType === 'first' ? 'AI Chat' : 'Prediction'} Credits</span>
              </div>
            </div>
          )}
          
          <Button 
            className="w-full h-[48px] text-[16px] font-medium rounded-full bg-gradient-to-r from-[#8C01FA] to-[#7f20ef] text-white"
            onClick={() => router.push('/main')}
          >
            Return to Dashboard
          </Button>
        </div>
      </div>
    </div>
  )
}