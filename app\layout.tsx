import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import ConfigureAmplifyClientSide from "@/app/auth/page";
import "./globals.css"
import { Toaster } from "@/components/ui/sonner"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "DarkHorse - AI-Powered Sports Predictions",
  description: "Get AI-powered sports predictions for smarter decision-making",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <ConfigureAmplifyClientSide>
          {children}
          <Toaster />
        </ConfigureAmplifyClientSide>
      </body>
    </html>
  )
}



import './globals.css'
