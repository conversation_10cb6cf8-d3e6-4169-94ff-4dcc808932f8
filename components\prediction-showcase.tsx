import PredictionCard from "./prediction-card"
import TrophyIcon from "./svg/trophy-icon"
import DiagramIcon from "./svg/diagram-icon"
import ArrowIcon from "./svg/arrow-icon"
import MessageIcon from "./svg/message-icon"

export default function PredictionShowcase() {
  return (
    <section className="w-full py-20 md:py-28 relative">
      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "60%",
          right: "40px",
          transform: "translateY(-50%) rotate(-164.56deg)",
          background: "#2CAD9C",
          zIndex: "0",
        }}
      />
      
      <div className="container mx-auto px-4 max-w-[1200px] relative">
        <div className="text-center mb-6">
        <h2 className="text-center text-[28px] md:text-5xl font-bold mb-4">
          <span className="bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] bg-clip-text text-transparent">
            DarkHorse Your Picks
          </span>
        </h2>
        </div>

        <div className="text-center mb-20 max-w-3xl mx-auto">
          <p className="text-white text-[18px] md:text-2xl mb-2">Unlock Exclusive AI-Powered Insights</p>
          <p className="text-[#FFFFFF80] text-[14px] inline-block px-3 py-1 rounded-full">
            (auto-updating feed for upcoming game data)
          </p>
        </div>

        <div className="flex flex-col lg:flex-row items-center justify-between mb-20">
          <div className="w-full lg:w-1/2 mb-10 lg:mb-0 text-left">
            <div className="grid grid-cols-2 gap-8 md:hidden">
              <div className="flex flex-col items-center text-center">
                <TrophyIcon className="h-10 w-10 mb-4" />
                <h3 className="text-white text-base font-medium">Predicted Winner</h3>
                <h3 className="text-white text-base font-medium">& Confidence Score</h3>
              </div>

              <div className="flex flex-col items-center text-center">
                <DiagramIcon className="h-10 w-10 mb-4" />
                <h3 className="text-white text-base font-medium">Moneyline</h3>
                <h3 className="text-white text-base font-medium">& Spread Forecasts</h3>
              </div>

              <div className="flex flex-col items-center text-center">
                <ArrowIcon className="h-10 w-10 mb-4" />
                <h3 className="text-white text-base font-medium">Total Score</h3>
                <h3 className="text-white text-base font-medium">(Over/Under) Projection</h3>
              </div>

              <div className="flex flex-col items-center text-center">
                <MessageIcon className="h-10 w-10 mb-4" />
                <h3 className="text-white text-base font-medium">AI Chatbot:</h3>
                <p className="text-white text-sm">Ask for stats, trends, or matchup analysis — instantly</p>
              </div>
            </div>

            <div className="hidden md:grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-16">
              <div>
                <TrophyIcon className="h-8 w-8 mb-4" />
                <h3 className="text-white text-xl font-medium mb-2">Predicted Winner</h3>
                <h3 className="text-white text-xl font-medium">& Confidence Score</h3>
              </div>

              <div>
                <DiagramIcon className="h-8 w-8 mb-4" />
                <h3 className="text-white text-xl font-medium mb-2">Moneyline</h3>
                <h3 className="text-white text-xl font-medium">& Spread Forecasts</h3>
              </div>

              <div>
                <ArrowIcon className="h-8 w-8 mb-4" />
                <h3 className="text-white text-xl font-medium mb-2">Total Score</h3>
                <h3 className="text-white text-xl font-medium">(Over/Under) Projection</h3>
              </div>

              <div>
                <MessageIcon className="h-8 w-8 mb-4" />
                <h3 className="text-white text-xl font-medium mb-2">AI Chatbot:</h3>
                <p className="text-white text-lg">Ask for stats, trends, or matchup analysis — instantly</p>
              </div>
            </div>
          </div>

          <div className="w-full lg:w-1/2">
            <PredictionCard />
          </div>
        </div>

        <div className="mt-20 text-center">
          <p className="text-white text-xl md:text-2xl mb-6">Know Before You Act – Unbiased, Data-Driven Analytics</p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 w-full">
          <button className="bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white text-[16px] sm:text-[14px] font-medium rounded-full w-full sm:w-[224px] h-[56px] sm:h-[48px] flex items-center justify-center hover:opacity-90 transition-opacity">
            Get AI Predictions
          </button>
          <button className="border border-white/20 bg-transparent text-white text-[16px] sm:text-[14px] font-medium rounded-full w-full sm:w-[224px] h-[56px] sm:h-[48px] flex items-center justify-center hover:bg-white/10 transition-all">
            Sign Up for Exclusive Access
          </button>
        </div>
        </div>
      </div>
    </section>
  )
}


