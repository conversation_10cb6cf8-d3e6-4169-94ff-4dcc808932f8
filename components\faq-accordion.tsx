"use client"

import { useState } from "react"
import { ChevronDown, ChevronRight } from "lucide-react"

type FAQItem = {
    question: string
    answer: string | JSX.Element
}

export default function FAQAccordion() {
    const [openIndex, setOpenIndex] = useState<number>(0)

    const faqItems: FAQItem[] = [
        {
            question: "Which sports and leagues are covered in the predictions?",
            answer: (
                <p>
                    Our AI-powered predictions cover major leagues such as the NFL, NBA, MLB, and MLS, along with other sports
                    supported by the API. Predictions include team matchups, historical performance, and key player insights.
                </p>
            ),
        },
        {
            question: "What types of game predictions are available?",
            answer: (
                <p>
                    DarkHorse offers a comprehensive range of predictions including moneyline (winner), point spreads, over/under
                    totals, and player performance metrics. Each prediction comes with a confidence score based on our AI's
                    analysis of historical and current data.
                </p>
            ),
        },
        {
            question: "How does the AI determine the predicted winner?",
            answer: (
                <p>
                    Our AI analyzes thousands of data points including team performance, player statistics, historical matchups,
                    recent form, injuries, and even venue conditions. The algorithm weighs these factors differently based on
                    their proven impact on outcomes, continuously learning and improving its accuracy with each game.
                </p>
            ),
        },
        {
            question: "Can I customize predictions based on my preferences?",
            answer: (
                <p>
                    Yes! Premium users can customize their prediction feed to focus on specific teams, leagues, or types of bets.
                    You can also set notification preferences to receive alerts for high-confidence predictions or games involving
                    your favorite teams.
                </p>
            ),
        },
    ]

    const toggleFAQ = (index: number) => {
        setOpenIndex(index === openIndex ? 0 : index)
    }

    return (
        <section className="w-full py-20 md:py-28 relative">
            <div className="container mx-auto px-4 max-w-[1200px] relative">
                <div className="max-w-3xl mx-auto space-y-6">
                    {faqItems.map((item, index) => (
                        <div
                            key={index}
                            className="rounded-lg overflow-hidden"
                            style={{
                                background: "#00000047",
                                backdropFilter: "blur(20px)",
                                WebkitBackdropFilter: "blur(20px)", 
                                borderRadius: "24px"
                            }}
                        >
                            <button
                                className="flex justify-between items-center w-full text-left p-6 text-xl text-white cursor-pointer touch-manipulation"
                                onClick={() => toggleFAQ(index)}
                                type="button"
                                aria-expanded={openIndex === index}
                            >
                                {item.question}
                                <span className="ml-4 flex items-center justify-center">
                                    {openIndex === index ? (
                                        <ChevronDown 
                                            className="h-6 w-6 text-white p-1 rounded-full transition-transform duration-300 ease-in-out rotate-0" 
                                            style={{ background: "#FFFFFF0A" }} 
                                        />
                                    ) : (
                                        <ChevronRight
                                            className="h-6 w-6 text-white p-1 rounded-full transition-transform duration-300 ease-in-out" 
                                            style={{ background: "#FFFFFF0A" }} 
                                        />
                                    )}
                                </span>
                            </button>

                            <div 
                                className={`px-6 overflow-hidden transition-all duration-300 ease-in-out ${
                                    openIndex === index 
                                        ? 'max-h-[500px] opacity-100' 
                                        : 'max-h-0 opacity-0'
                                }`}
                            >
                                <div className="text-white/80 pb-6">
                                    {item.answer}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </section >
    )
}
