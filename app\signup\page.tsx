"use client"

import Image from "next/image"
import Link from "next/link"
import { Eye, EyeOff } from "lucide-react"
import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { signUp, signInWithRedirect, getCurrentUser } from "aws-amplify/auth"
import { Hub } from "aws-amplify/utils"
import { UserService } from "@/services/api/users"
import type { CreateUserRequest } from "@/types/api"
import { Button } from "@/components/ui/button"
import { toast } from "sonner"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"

export default function SignUpPage() {
  const router = useRouter()
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [phone, setPhone] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [phoneError, setPhoneError] = useState<string | null>(null)
  const [passwordError, setPasswordError] = useState<string | null>(null)
  const [passwordRequirements, setPasswordRequirements] = useState({
    length: false,
    lowercase: false,
    uppercase: false,
    number: false,
    special: false
  })
  const [emailError, setEmailError] = useState<string | null>(null)
  const [user, setUser] = useState<any>(null)
  const [oauthError, setOauthError] = useState<string | null>(null)
  const [customState, setCustomState] = useState<any>(null)
  const [showTermsModal, setShowTermsModal] = useState(false)
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const unsubscribe = Hub.listen("auth", ({ payload }) => {
      switch (payload.event) {
        case "signInWithRedirect":
          getUser()
          break
        case "signInWithRedirect_failure":
          setOauthError("An error has occurred during the OAuth flow")
          toast.error("Failed to sign in")
          break
        case "customOAuthState":
          setCustomState(payload.data)
          break
      }
    })

    return unsubscribe
  }, [])

  const getUser = async () => {
    try {
      const currentUser = await getCurrentUser()
      setUser(currentUser)
      toast.success('Login successful!', {
        duration: 3000,
        style: {
          backgroundColor: 'linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)',
          borderWidth: '1px',
          borderStyle: 'solid',
          borderColor: 'rgba(25, 251, 155, 0.2)',
          backdropFilter: 'blur(8px)',
        },
      })
      router.push('/main')
    } catch (error) {
      console.error(error)
      console.log("Not signed in")
    }
  }

  const handleGoogleSignIn = async () => {
    try {
      await signInWithRedirect({
        provider: 'Google',
      })
    } catch (error) {
      console.error('Google sign in error:', error)
      toast.error('Failed to sign in with Google')
    }
  }

  const handleFacebookSignIn = async () => {
    try {
      await signInWithRedirect({
        provider: 'Facebook',
      })
    } catch (error) {
      console.error('Facebook sign in error:', error)
      toast.error('Failed to sign in with Facebook')
    }
  }

  const formatPhoneNumber = (phone: string) => {
    const digits = phone.replace(/\D/g, '')

    if (phone.startsWith('+')) {
      return phone
    }

    return `+1${digits}`
  }

  const validatePhone = (phone: string) => {
    const phoneRegex = /^\+?1?\d{10,12}$/
    const formattedPhone = phone.replace(/\D/g, '')
    if (!phoneRegex.test(formattedPhone)) {
      setPhoneError("Please enter a valid phone number")
      return false
    }
    setPhoneError(null)
    return true
  }

  const validatePassword = (password: string) => {
    const requirements = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      number: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    }

    setPasswordRequirements(requirements)

    if (!requirements.length) {
      setPasswordError("Password must be at least 8 characters long")
      return false
    }
    if (!requirements.lowercase) {
      setPasswordError("Password must have lowercase characters")
      return false
    }
    if (!requirements.uppercase) {
      setPasswordError("Password must have uppercase characters")
      return false
    }
    if (!requirements.number) {
      setPasswordError("Password must have numeric characters")
      return false
    }
    if (!requirements.special) {
      setPasswordError("Password must have special characters")
      return false
    }

    setPasswordError(null)
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    setPasswordError(null)
    setPhoneError(null)
    setEmailError(null)

    // Validate all fields
    const isPhoneValid = validatePhone(phone)
    const isPasswordValid = validatePassword(password)

    if (password !== confirmPassword) {
      setPasswordError("Passwords don't match")
      setIsLoading(false)
      return
    }

    if (!isPhoneValid || !isPasswordValid) {
      setIsLoading(false)
      return
    }

    setIsLoading(false)
    setShowTermsModal(true)
  }

  const handleTermsScroll = (e: any) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
    const isAtBottom = scrollTop + clientHeight >= scrollHeight * 0.9
    if (isAtBottom && !hasScrolledToBottom) {
      setHasScrolledToBottom(true)
    }
  }

  const handleAcceptTerms = async () => {
    setShowTermsModal(false)
    setIsLoading(true)
    
    try {
      const formattedPhone = formatPhoneNumber(phone)

      try {
        const { isSignUpComplete } = await signUp({
          username: email,
          password,
          options: {
            userAttributes: {
              email,
              phone_number: formattedPhone,
              name: `${firstName} ${lastName}`,
            },
            autoSignIn: true
          }
        })

        if (isSignUpComplete) {
          toast.success('Signup successful!', {
            duration: 3000,
            style: {
              background: 'linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)',
              border: '1px solid rgba(25, 251, 155, 0.2)',
              backdropFilter: 'blur(8px)',
            },
          })
          router.push('/onboarding')
        } else {
          router.push(`/verify-email?email=${encodeURIComponent(email)}`)
        }
      } catch (error: any) {
        console.error('SignUp Error:', error)
        if (error.name === 'UsernameExistsException') {
          toast.error('This email is already registered', { duration: 3000 })
          setEmailError('This email is already registered')
          return
        } else {
          throw error
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred during sign up'
      setError(errorMessage)
      setPasswordError(errorMessage.includes('Password did not conform') ? errorMessage : null)
    } finally {
      setIsLoading(false)
    }
  }

  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const [isFormFilled, setIsFormFilled] = useState(false)

  useEffect(() => {
    setIsFormFilled(
      firstName.length > 0 &&
      lastName.length > 0 &&
      email.length > 0 &&
      phone.length > 0 &&
      password.length > 0 &&
      confirmPassword.length > 0
    )
  }, [firstName, lastName, email, phone, password, confirmPassword])

  return (
    <div className="flex min-h-screen md:max-h-screen bg-[#050816] p-[20px] gap-[40px] relative overflow-hidden">
      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "10px",
          left: "0%",
          background: "#2CAD9C",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />

      <div
        className="absolute opacity-30 blur-[150px]"
        style={{
          width: "362.36px",
          height: "252.34px",
          top: "450px",
          right: "0%",
          background: "#7F20EF",
          transform: "rotate(-164.56deg)",
          zIndex: "0",
        }}
      />
      <div className="w-full lg:w-1/2 flex items-center justify-center relative z-10">
        <div className="w-full max-w-[540px] flex flex-col items-center">
          <Link href="/" className="flex items-center mb-2 md:mb-2">
            <div
              className="flex items-center rounded-[100px] w-[188px] h-[40px] px-6 relative"
              style={{
                gap: "5px",
                background:
                  "linear-gradient(261.81deg, rgba(25, 251, 155, 0.1) -8.01%, rgba(140, 1, 250, 0.1) 100%)",
              }}
            >
              <div
                style={{
                  position: "absolute",
                  top: 0,
                  right: 0,
                  bottom: 0,
                  left: 0,
                  borderRadius: "100px",
                  padding: "1.5px",
                  background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                  WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                  WebkitMaskComposite: "xor",
                  maskComposite: "exclude",
                  pointerEvents: "none",
                }}
              />
              <Image src="https://borisbelov.com/dev/darkhorsewin/darkhorselogo.svg" alt="DarkHorse Icon" width={24} height={26} />
              <Image src="/DARKHORSE.svg" alt="DarkHorse" width={120} height={20} />
            </div>
          </Link>

          <div className="space-y-1">
            <div className="text-center">
              <h1 className="text-[32px] font-bold text-white">Registration</h1>
            </div>

            <div className="space-y-2 md:space-y-2 w-full">
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-2">
                  <label htmlFor="firstName" className="block text-sm font-medium text-white">
                    First Name
                  </label>
                  <input
                    id="firstName"
                    type="text"
                    placeholder="John"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    className="w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border border-[#282828] text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="lastName" className="block text-sm font-medium text-white">
                    Last Name
                  </label>
                  <input
                    id="lastName"
                    type="text"
                    placeholder="Doe"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    className="w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border border-[#282828] text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-medium text-white">
                  Email
                </label>
                <div className="relative">
                  <input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => {
                      setEmail(e.target.value)
                      setEmailError(null) // Clear error when user types
                    }}
                    className={`w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border ${emailError ? 'border-red-500' : 'border-[#282828]'
                      } text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]`}
                  />
                  {emailError && (
                    <div className="flex items-center mt-1">
                      <p className="text-sm text-red-500">{emailError}</p>
                      <Link href="/login" className="ml-2 text-sm text-[#7f20ef] hover:underline">
                        Sign in instead
                      </Link>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="phone" className="block text-sm font-medium text-white">
                  Phone Number
                </label>
                <div className="relative">
                  <input
                    id="phone"
                    type="tel"
                    placeholder="+****************"
                    value={phone}
                    onChange={(e) => {
                      setPhone(e.target.value)
                      validatePhone(e.target.value)
                    }}
                    pattern="[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}"
                    title="Please enter a valid phone number (e.g., +****************)"
                    className={`w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border ${phoneError ? 'border-red-500' : 'border-[#282828]'
                      } text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]`}
                  />
                  {phoneError && (
                    <p className="mt-1 text-sm text-red-500">{phoneError}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="block text-sm font-medium text-white">
                  Password
                </label>
                <div className="relative">
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value)
                      validatePassword(e.target.value)
                    }}
                    className={`w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border ${passwordError ? 'border-red-500' : 'border-[#282828]'
                      } text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]`}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
                  >
                    {showPassword ? (
                      <EyeOff size={20} />
                    ) : (
                      <Eye size={20} />
                    )}
                  </button>
                  {passwordError && (
                    <p className="mt-1 text-sm text-red-500">{passwordError}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-white">
                  Confirm Password
                </label>
                <div className="relative">
                  <input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm your password"
                    value={confirmPassword}
                    onChange={(e) => {
                      setConfirmPassword(e.target.value)
                      if (password !== e.target.value) {
                        setPasswordError("Passwords don't match")
                      } else {
                        setPasswordError(null)
                      }
                    }}
                    className={`w-full h-12 px-4 rounded-[10px] bg-[#28282847] backdrop-blur-[20px] border ${passwordError ? 'border-red-500' : 'border-[#282828]'
                      } text-white placeholder:text-[#4b5563] focus:outline-none focus:ring-1 focus:ring-[#7f20ef] focus:border-[#7f20ef]`}
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
                  >
                    {showConfirmPassword ? (
                      <EyeOff size={20} />
                    ) : (
                      <Eye size={20} />
                    )}
                  </button>
                </div>
              </div>

              <button
                onClick={handleSubmit}
                disabled={isLoading}
                className={`w-full h-12 rounded-[70px] backdrop-blur-[20px] transition-all duration-300 flex items-center justify-center gap-[15.16px] ${isFormFilled
                  ? "bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white"
                  : "bg-[#28282880] text-[#9ca3af]"
                  }`}
              >
                {isLoading ? "Signing up..." : "Sign up"}
              </button>

              <div className="relative flex items-center justify-center my-2">
                <div className="flex-grow">
                  <div
                    style={{
                      height: '1px',
                      background: 'linear-gradient(267.78deg, #19FB9B 1.87%, #8C01FA 98.13%)',
                    }}
                  />
                </div>
                <div className="bg-transparent px-4 text-[#9ca3af] text-sm">Or</div>
                <div className="flex-grow">
                  <div
                    style={{
                      height: '1px',
                      background: 'linear-gradient(267.78deg, #8C01FA 1.87%, #19FB9B 98.13%)',
                    }}
                  />
                </div>
              </div>

              {/* Social login buttons - responsive to screen height */}
              <style jsx>{`
                @media (max-height: 800px) {
                  .social-buttons-container {
                    flex-direction: row;
                  }
                  .button-text-long {
                    display: none;
                  }
                  .button-text-short {
                    display: inline;
                  }
                }
                @media (min-height: 801px) {
                  .social-buttons-container {
                    flex-direction: column;
                  }
                  .button-text-long {
                    display: inline;
                  }
                  .button-text-short {
                    display: none;
                  }
                }
              `}</style>
              
              <div className="flex gap-2 social-buttons-container">
                <button
                  onClick={handleGoogleSignIn}
                  className="w-full h-12 rounded-[70px] bg-[#28282880] backdrop-blur-[20px] text-white hover:opacity-90 transition-opacity flex items-center justify-center gap-[15.16px]"
                >
                  <Image
                    src="/Google.svg"
                    alt="Google Icon"
                    width={27}
                    height={27}
                  />
                  <span className="button-text-long">Sign in with Google</span>
                  <span className="button-text-short">Google</span>
                </button>

                <button
                  onClick={handleFacebookSignIn}
                  className="w-full h-12 rounded-[70px] bg-[#28282880] backdrop-blur-[20px] text-white hover:opacity-90 transition-opacity flex items-center justify-center gap-[15.16px]"
                >
                  <Image
                    src="/Facebook.svg"
                    alt="Facebook Icon"
                    width={27}
                    height={27}
                  />
                  <span className="button-text-long">Sign in with Facebook</span>
                  <span className="button-text-short">Facebook</span>
                </button>
              </div>

              <div className="text-center pt-4 md:pt-4">
                <p className="text-white text-sm">
                  Already have an account?{" "}
                  <Link href="/login" className="text-[#9ca3af] hover:underline">
                    Sign in
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="hidden lg:block w-1/2">
        <Image
          src="/login-image.webp"
          alt="Dark Horse App"
          width={800}
          height={800}
          className="w-full h-full"
          priority
        />
      </div>

      <Dialog open={showTermsModal} onOpenChange={setShowTermsModal} >
        <DialogContent className="sm:max-w-3xl bg-[#0f1724] border-[#282828] text-white p-0 max-h-[90vh]">
          <div className="p-6 border-b border-[#1a2436] text-center">
            <h1 className="text-[24px] font-bold">Terms and Conditions</h1>
          </div>
          
          <ScrollArea 
            className="h-[60vh]" 
            onScrollCapture={handleTermsScroll}
            ref={scrollAreaRef}
          >
            <style jsx global>{`
              .scrollbar-thumb {
                background: linear-gradient(38.98deg, #19FB9B -34.05%, #8C01FA 77.64%) !important;
                width: 2px !important;
                border-radius: 88px !important;
              }
            `}</style>
            <div className="p-6 space-y-6 bg-[#111822]">
              <p className="text-gray-400">Effective Date: 1/18/2025</p>
              
              <p>
                Welcome to DarkHorsewin.com ("we," "us," or "our"). These Terms and Conditions ("Terms") govern your access to and use of our software-as-a-service (SaaS) platform, website, and related services (collectively, the "Services"). By creating an account, logging in, or otherwise accessing or using the Services, you agree to be bound by these Terms.
              </p>
              <p className="font-bold">
                If you do not agree to these Terms, do not use the Services.
              </p>
              
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">1. Acceptance of Terms</h2>
                <p>
                  By registering for an account or accessing the Services, you confirm that:
                </p>
                <ul className="list-disc pl-6 space-y-1">
                  <li>You are at least 18 years old or the age of majority in your jurisdiction.</li>
                  <li>You have the authority to bind yourself or the organization you represent to these Terms.</li>
                </ul>
              </div>
              
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">2. Account Registration and Security</h2>
                <p>
                  <span className="font-medium">2.1 Account Information:</span> To use the Services, you must create an account and provide accurate, complete, and up-to-date information. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.
                </p>
                <p>
                  <span className="font-medium">2.2 Unauthorized Access:</span> Notify us immediately if you suspect unauthorized access to your account. We are not liable for any loss or damage resulting from unauthorized access or your failure to secure your account.
                </p>
              </div>
              
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">3. License and Use of the Services</h2>
                <p>
                  <span className="font-medium">3.1 License:</span> Subject to these Terms, we grant you a non-exclusive, non-transferable, revocable license to access and use the Services for your internal business purposes.
                </p>
              </div>
            </div>
          </ScrollArea>
          
          <div className="p-6 border-t border-[#1a2436] flex justify-between">
            <Button 
              variant="outline" 
              className="w-[140px] h-[50px] rounded-[88px] gap-[8px] px-[8px] py-[8px] border border-white text-gray-300 hover:bg-gray-800 hover:text-white"
              onClick={() => setShowTermsModal(false)}
            >
              Cancel
            </Button>
            
            <Button 
              className="w-[140px] h-[50px] rounded-[88px] gap-[8px] px-[8px] py-[8px] bg-gradient-to-r from-[#19FB9B] to-[#8C01FA] text-white hover:opacity-90"
              onClick={handleAcceptTerms}
              disabled={!hasScrolledToBottom || isLoading}
            >
              {isLoading ? "Processing..." : hasScrolledToBottom ? "Accept" : "Scroll to accept"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
