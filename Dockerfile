FROM node:22-alpine AS base

# Stage 1: Install dependencies
FROM base AS deps
WORKDIR /app
COPY package.json package-lock.json pnpm-lock.yaml ./
RUN corepack enable pnpm && pnpm install

# Stage 2: Build the application
FROM base AS builder
WORKDIR /app

COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Create a default .env file for build
RUN echo "NEXT_PUBLIC_API_URL=https://api.darkhorsewin.com" > .env \
    && echo "NEXT_PUBLIC_ASSET_PREFIX=static" >> .env \
    && echo "NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-2_WmA8sB9bq" >> .env \
    && echo "NEXT_PUBLIC_COGNITO_CLIENT_ID=28fo280tto9lbb1e3et75fts2u" >> .env

# Enable pnpm and build
RUN corepack enable pnpm && NODE_ENV=production pnpm run build

# Stage 3: Production server
FROM base AS runner
WORKDIR /app
ENV NODE_ENV=production
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

ENV PORT=3005
EXPOSE 3005
CMD ["node", "server.js"]
