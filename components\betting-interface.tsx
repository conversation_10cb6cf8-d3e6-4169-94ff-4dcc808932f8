"use client"

import { useState } from "react"
import BetCard from "./bet-card"
import { But<PERSON> } from "@/components/ui/button"

export default function BettingInterface() {
  const [bets, setBets] = useState([
    { id: 1, team: "DEN Nuggets", opponent: "IND Pacers", spread: -5, odds: -110, amount: 50 },
    { id: 2, team: "DEN Nuggets", opponent: "IND Pacers", spread: -5, odds: -110, amount: 50 },
    { id: 3, team: "DEN Nuggets", opponent: "IND Pacers", spread: -5, odds: -110, amount: 50 },
    { id: 4, team: "DEN Nuggets", opponent: "IND Pacers", spread: -5, odds: -110, amount: 50 },
  ])

  const removeBet = (id: number) => {
    setBets(bets.filter((bet) => bet.id !== id))
  }

  const updateBetAmount = (id: number, amount: number) => {
    setBets(bets.map((bet) => (bet.id === id ? { ...bet, amount } : bet)))
  }

  const totalAmount = bets.reduce((sum, bet) => sum + bet.amount, 0)
  const potentialWinnings = totalAmount * 1.88

  return (
    <div className="w-full max-w-md flex flex-col gap-4">
      {bets.map((bet) => (
        <BetCard
          key={bet.id}
          bet={bet}
          onRemove={() => removeBet(bet.id)}
          onUpdateAmount={(amount) => updateBetAmount(bet.id, amount)}
        />
      ))}

      <div className="flex justify-between text-white">
        <div>
          <p className="text-[12px] text-gray-400">Total:</p>
          <p className="text-[12px] md:text-[14px] font-bold">{totalAmount.toFixed(2)} USD</p>
        </div>
        <div className="text-right">
          <p className="text-[12px] text-gray-400">Potential winnings:</p>
          <p className="text-[12px] md:text-[14px] font-bold">{potentialWinnings.toFixed(2)} USD</p>
        </div>
      </div>
      <Button className="w-full bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white hover:opacity-90 rounded-[70px] backdrop-blur-[20px] transition-all duration-300">
        Buy Prediction
      </Button>
    </div>
  )
}