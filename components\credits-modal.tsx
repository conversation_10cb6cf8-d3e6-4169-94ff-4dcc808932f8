"use client"

import { useState } from "react"
import Image from "next/image"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { getStripe } from "@/lib/stripe"
import { getCurrentUser } from "aws-amplify/auth"

const CREDIT_PACKAGES = {
  first: [
    { id: "ai-10", amount: 10, price: 4.99, stripePriceId: "price_1OXYZABCDEFGHIJKLMNOPQRs", description: "Perfect for casual users" },
    { id: "ai-50", amount: 50, price: 19.99, stripePriceId: "price_1OXYZABCDEFGHIJKLMNOPQRt", description: "Most popular option", bestValue: true },
    { id: "ai-100", amount: 100, price: 34.99, stripePriceId: "price_1OXYZABCDEFGHIJKLMNOPQRu", description: "For power users" }
  ],
  second: [
    { id: "pred-10", amount: 10, price: 4.99, stripePriceId: "price_1OXYZABCDEFGHIJKLMNOPQRv", description: "Perfect for casual users" },
    { id: "pred-50", amount: 50, price: 19.99, stripePriceId: "price_1OXYZABCDEFGHIJKLMNOPQRw", description: "Most popular option", bestValue: true },
    { id: "pred-100", amount: 100, price: 34.99, stripePriceId: "price_1OXYZABCDEFGHIJKLMNOPQRx", description: "For power users" }
  ]
};

interface CreditsModalProps {
  isOpen: boolean
  onClose: () => void
  initialTab?: "first" | "second"
}

export default function CreditsModal({ isOpen, onClose, initialTab = "first" }: CreditsModalProps) {
  const [activeTab, setActiveTab] = useState<"first" | "second">(initialTab)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handlePurchase = async (packageInfo: typeof CREDIT_PACKAGES.first[0]) => {
    try {
      setIsLoading(true)
      
      const user = await getCurrentUser()
      const userId = user.userId
      
      if (!userId) {
        toast.error("Please log in to purchase credits")
        router.push("/login")
        return
      }
      
      const response = await fetch("/api/create-checkout-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          creditType: activeTab,
          amount: packageInfo.amount,
          priceId: packageInfo.stripePriceId,
          quantity: 1,
          userId: userId
        }),
      })
      
      const { sessionId, url } = await response.json()
      
      if (url) {
        window.location.href = url
      } else {
        const stripe = await getStripe()
        const { error } = await stripe!.redirectToCheckout({ sessionId })
        
        if (error) {
          toast.error(error.message || "Something went wrong")
        }
      }
    } catch (error) {
      console.error("Error creating checkout session:", error)
      toast.error("Failed to initiate checkout")
    } finally {
      setIsLoading(false)
    }
  }

  return isOpen ? (
    <>
      <style jsx global>{`
        @media (max-height: 800px) {
          .credits-modal-content {
            padding: 8px !important;
          }
          .credits-modal-content h2 {
            font-size: 1rem !important;
          }
          .credits-modal-content .text-lg {
            font-size: 0.875rem !important;
          }
          .credits-modal-content .text-xl {
            font-size: 1rem !important;
          }
          .credits-modal-content .text-sm {
            font-size: 0.75rem !important;
          }
          .credits-modal-content .p-6 {
            padding: 0.75rem !important;
          }
          .credits-modal-content .p-4 {
            padding: 0.5rem !important;
          }
          .credits-modal-content .space-y-6 > div {
            margin-top: 0.5rem !important;
            margin-bottom: 0.5rem !important;
          }
          .credits-modal-content .h-[48px] {
            height: 36px !important;
          }
          .credits-modal-content .h-[80vh] {
            height: 70vh !important;
          }
        }
      `}</style>
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="sm:max-w-[425px] bg-[#08101e] border-none p-0 text-white credits-modal-content">
          <div className="flex flex-col h-[80vh]">
            <div className="items-center justify-center p-4 border-b border-[#1a2436]">
              <h2 className="text-xl font-medium text-center">Purchase Credits</h2>
              <div className="flex flex-row justify-center gap-1 mt-1">
                <h2 className="text-lg text-center text-gray-400">Unlock premium features</h2>
              </div>
            </div>

            <Tabs 
              defaultValue={initialTab} 
              className="flex-1 flex flex-col"
              onValueChange={(value) => setActiveTab(value as "first" | "second")}
            >
              <div className="border-b border-[#1a2436]">
                <TabsList className="w-full bg-transparent h-14">
                  <TabsTrigger 
                    value="first" 
                    className="flex-1 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 rounded-none h-full"
                    style={{ 
                      borderColor: activeTab === "first" ? "#7f20ef" : "transparent",
                      color: activeTab === "first" ? "#fff" : "#9CA2B5"
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[#7f20ef]">
                        <Image
                          src="/star-first.svg"
                          alt="AI Chat Credits"
                          width={16}
                          height={16}
                        />
                      </div>
                      <span>AI Chat Credits</span>
                    </div>
                  </TabsTrigger>
                  <TabsTrigger 
                    value="second" 
                    className="flex-1 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 rounded-none h-full"
                    style={{ 
                      borderColor: activeTab === "second" ? "#ffd900" : "transparent",
                      color: activeTab === "second" ? "#fff" : "#9CA2B5"
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[#ffd900]">
                        <Image
                          src="/star-second.svg"
                          alt="Prediction Credits"
                          width={16}
                          height={16}
                        />
                      </div>
                      <span>Prediction Credits</span>
                    </div>
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="first" className="flex-1 flex flex-col mt-0 data-[state=inactive]:hidden">
                <div className="flex-1 p-6 overflow-auto">
                  <div className="space-y-6">
                    <div className="bg-[#FFFFFF0F] p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <Image
                            src="/star-first.svg"
                            alt="Credit Icon"
                            width={24}
                            height={24}
                          />
                          <span className="text-xl font-medium">10 Credits</span>
                        </div>
                        <span className="text-xl font-medium">$4.99</span>
                      </div>
                      <p className="text-gray-400 text-sm">Perfect for casual users</p>
                    </div>

                    <div className="bg-[#FFFFFF0F] p-4 rounded-lg border-2 border-[#7f20ef]">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <Image
                            src="/star-first.svg"
                            alt="Credit Icon"
                            width={24}
                            height={24}
                          />
                          <span className="text-xl font-medium">50 Credits</span>
                        </div>
                        <span className="text-xl font-medium">$19.99</span>
                      </div>
                      <p className="text-gray-400 text-sm">Most popular option</p>
                      <div className="mt-2 bg-[rgb(196,142,255)]/20 p-1 rounded text-center text-[rgb(196,142,255)] text-sm">
                        Best Value
                      </div>
                    </div>

                    <div className="bg-[#FFFFFF0F] p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <Image
                            src="/star-first.svg"
                            alt="Credit Icon"
                            width={24}
                            height={24}
                          />
                          <span className="text-xl font-medium">100 Credits</span>
                        </div>
                        <span className="text-xl font-medium">$34.99</span>
                      </div>
                      <p className="text-gray-400 text-sm">For power users</p>
                    </div>
                  </div>
                </div>

                <div className="p-4 border-t border-[#1a2436]">
                  <Button 
                    className="w-full h-[48px] text-[16px] font-medium rounded-full bg-gradient-to-r from-[#8C01FA] to-[#7f20ef] text-white"
                  >
                    Purchase AI Chat Credits
                  </Button>
                  <p className="text-center text-gray-400 text-sm mt-2">
                    Secure payment processing via Stripe
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="second" className="flex-1 flex flex-col mt-0 data-[state=inactive]:hidden">
                <div className="flex-1 p-6 overflow-auto">
                  <div className="space-y-6">
                    <div className="bg-[#FFFFFF0F] p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <Image
                            src="/star-second.svg"
                            alt="Credit Icon"
                            width={24}
                            height={24}
                          />
                          <span className="text-xl font-medium">10 Credits</span>
                        </div>
                        <span className="text-xl font-medium">$4.99</span>
                      </div>
                      <p className="text-gray-400 text-sm">Perfect for casual users</p>
                    </div>

                    <div className="bg-[#FFFFFF0F] p-4 rounded-lg border-2 border-[#ffd900]">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <Image
                            src="/star-second.svg"
                            alt="Credit Icon"
                            width={24}
                            height={24}
                          />
                          <span className="text-xl font-medium">50 Credits</span>
                        </div>
                        <span className="text-xl font-medium">$19.99</span>
                      </div>
                      <p className="text-gray-400 text-sm">Most popular option</p>
                      <div className="mt-2 bg-[#ffd900]/20 p-1 rounded text-center text-[#ffd900] text-sm">
                        Best Value
                      </div>
                    </div>

                    <div className="bg-[#FFFFFF0F] p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <Image
                            src="/star-second.svg"
                            alt="Credit Icon"
                            width={24}
                            height={24}
                          />
                          <span className="text-xl font-medium">100 Credits</span>
                        </div>
                        <span className="text-xl font-medium">$34.99</span>
                      </div>
                      <p className="text-gray-400 text-sm">For power users</p>
                    </div>
                  </div>
                </div>

                <div className="p-4 border-t border-[#1a2436]">
                  <Button 
                    className="w-full h-[48px] text-[16px] font-medium rounded-full bg-gradient-to-r from-[#ffd900] to-[#ffb800] text-black"
                    onClick={() => {
                      const selectedPackage = CREDIT_PACKAGES.second.find(pkg => pkg.amount === 50) || CREDIT_PACKAGES.second[1];
                      handlePurchase(selectedPackage);
                    }}
                    disabled={isLoading}
                  >
                    {isLoading ? "Processing..." : "Purchase Prediction Credits"}
                  </Button>
                  <p className="text-center text-gray-400 text-sm mt-2">
                    Secure payment processing via Stripe
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>
    </>
  ) : null
}
