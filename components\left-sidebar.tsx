"use client"

import Image from "next/image"
import Link from "next/link"
import { useState } from "react"
import NFLIcon from "@/components/svg/nfl-team-icon"
import NBAIcon from "@/components/svg/nba-team-icon"
import MLBIcon from "@/components/svg/mlb-team-icon"
import MLSIcon from "@/components/svg/mls-team-icon"

export default function LeftSidebar() {
  const [selectedSection, setSelectedSection] = useState('all');

  const handleSectionClick = (section: string) => {
    setSelectedSection(section);
  };

  const renderSelectedBorder = (isSelected: boolean) => {
    if (!isSelected) return null;

    return (
      <div
        style={{
          position: "absolute",
          top: 0,
          right: 0,
          bottom: 0,
          left: 0,
          borderRadius: "8px",
          padding: "1.5px",
          background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
          WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
          WebkitMaskComposite: "xor",
          maskComposite: "exclude",
          pointerEvents: "none",
        }}
      />
    );
  };

  const sportLinks = [
    { id: 'all', label: 'All', count: 24, icon: '/icon-all' },
    { id: 'american-football', label: 'American Football (NFL)', count: 14, icon: '/icon-american-football' },
    { id: 'basketball', label: 'Basketball (NBA)', count: 11, icon: '/icon-basketball' },
    { id: 'football', label: 'Football (MLS)', count: 0, icon: '/icon-football' },
    { id: 'baseball', label: 'Baseball (MLB)', count: 0, icon: '/icon-baseball' }
  ];

  sportLinks[0].count = sportLinks.slice(1).reduce((sum, sport) => sum + sport.count, 0);

  return (
    <aside className="hidden 2xl:flex 2xl:w-[280px] h-screen flex-shrink-0 flex-col bg-[#08101e]">
      <div className="flex h-16 items-center justify-between gap-4 px-4">
        <Link href="/" className="flex items-center gap-2">
          <Image src="https://borisbelov.com/dev/darkhorsewin/darkhorselogo.svg" alt="DarkHorse Icon" width={30} height={32} />
          <Image src="/DARKHORSE.svg" alt="DarkHorse" width={120} height={20} />
        </Link>
      </div>

      <div className="flex flex-col p-4">
        <h2 className="mb-4 pb-4 text-[17px] font-medium border-b border-[#2D3643] border-solid">Prediction Sports</h2>
        <div className="space-y-3 text-[12px] text-center">
          {sportLinks.map((sport) => (
            <Link
              key={sport.id}
              href="#"
              onClick={() => handleSectionClick(sport.id)}
              className={`flex min-h-[50px] items-center justify-between rounded-[8px] p-2 relative ${selectedSection === sport.id ? '' : 'bg-[#3131314D]'
                }`}
              style={
                selectedSection === sport.id
                  ? {
                    background: "linear-gradient(0deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4))",
                    position: "relative",
                  }
                  : {}
              }
            >
              {renderSelectedBorder(selectedSection === sport.id)}
              <div className="flex justify-between w-full items-center gap-2 px-1">
                {sport.id === 'american-football' && (
                  <NFLIcon 
                    teamName={`nfl${selectedSection === sport.id ? '' : '-grey'}`} 
                    className="w-8 h-8"
                  />
                )}
                {sport.id === 'basketball' && (
                  <NBAIcon 
                    teamName={`nba${selectedSection === sport.id ? '' : '-grey'}`} 
                    className="w-8 h-8"
                  />
                )}
                {sport.id === 'baseball' && (
                  <MLBIcon 
                    teamName={`mlb${selectedSection === sport.id ? '' : '-grey'}`} 
                    className="w-8 h-8"
                  />
                )}
                {sport.id === 'football' && (
                  <MLSIcon 
                    teamName={`mls${selectedSection === sport.id ? '' : '-grey'}`} 
                    className="w-8 h-8"
                  />
                )}
                {sport.id === 'all' && (
                  <div className="w-6 h-6 flex items-center justify-center">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M4 6H20M4 12H20M4 18H20" stroke={selectedSection === sport.id ? "#FFFFFF" : "#9CA2B5"} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                )}
                <span className={selectedSection === sport.id ? 'text-[#2cad9c]' : 'text-[#FFFFFF4D]'}>
                  {sport.label}
                </span>
                <span className={selectedSection === sport.id ? 'text-[#2cad9c]' : 'text-[#FFFFFF4D]'}>
                  {sport.count}
                </span>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </aside>
  );
}



