"use client"

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { XCircle } from 'lucide-react'

export default function PaymentCanceledPage() {
  const router = useRouter()

  return (
    <div className="flex min-h-screen bg-[#050816] items-center justify-center p-4">
      <div className="w-full max-w-md bg-[#08101e] rounded-lg p-8 text-white">
        <div className="flex flex-col items-center text-center">
          <div className="mb-6 text-red-500">
            <XCircle size={64} />
          </div>
          
          <h1 className="text-2xl font-bold mb-2">Payment Canceled</h1>
          <p className="text-gray-400 mb-6">
            Your payment was canceled. No charges were made.
          </p>
          
          <Button 
            className="w-full h-[48px] text-[16px] font-medium rounded-full bg-gradient-to-r from-[#8C01FA] to-[#7f20ef] text-white"
            onClick={() => router.push('/main')}
          >
            Return to Dashboard
          </Button>
        </div>
      </div>
    </div>
  )
}