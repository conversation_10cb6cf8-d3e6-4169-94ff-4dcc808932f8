@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 240 33% 5%;
  --foreground: 0 0% 100%;
  --primary: 270 86% 53%;
  --secondary: 171 60% 43%;
  --accent: 224 65% 76%;
  --muted: 0 0% 16%;
  --muted-foreground: 220 14% 91%;
  --border: 0 0% 16%;
}

body {
  background-color: #050816;
  color: #ffffff;
}

.gradient-progress {
  background: linear-gradient(261.81deg, #8c01fa -8.01%, #19fb9b 100%);
  border-radius: 80px;
}

.normal-progress {
  background: linear-gradient(0deg, #B9BCBE, #B9BCBE);
  border-radius: 80px;
}

.gradient-text {
  background: linear-gradient(263.61deg, #19fb9b 25.9%, #8c01fa 79.13%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.gradient-button {
  background: linear-gradient(90deg, #7f20ef 0%, #2cad9c 100%);
}

.prediction-button {
  background: linear-gradient(261.81deg, #19fb9b -8.01%, #8c01fa 100%);
}

.border-gradient {
  border-image: linear-gradient(90deg, #7f20ef, #2cad9c) 1;
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(-8px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes menuBounce {
  0% {
    transform: scale(0.95) translateY(-4px);
  }

  50% {
    transform: scale(1.02) translateY(0);
  }

  100% {
    transform: scale(1) translateY(0);
  }
}

.menu-open {
  animation: menuBounce 0.4s ease-out forwards;
}

.tab-button {
  position: relative;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 700;
  color: #9CA2B5;
  background: transparent;
  width: 100%;
}

.tab-button.active {
  color: white;
}

.tab-button.active::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 8px;
  background: linear-gradient(0deg, rgba(11, 10, 10, 0.4), rgba(0, 0, 0, 0.4)),
    linear-gradient(261.81deg, rgba(25, 251, 155, 0.4) -8.01%, rgba(140, 1, 250, 0.4) 100%);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 8px;
  padding: 1.5px;
  background: linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

@media (min-width: 768px) {
  .tab-button-container {
    display: none;
  }

  .tab-content {
    display: block;
  }
}

@media (max-height: 300px) {
  .h-full {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  button,
  .icon {
    transform: scale(0.8);
  }

  .gap-4 {
    gap: 0.5rem !important;
  }

  .ScrollArea {
    max-height: 150px !important;
  }

  header,
  .header {
    min-height: 40px !important;
  }
}

@media (max-width: 400px) {

  .px-3,
  .px-4 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  .gap-4,
  .gap-3 {
    gap: 0.5rem !important;
  }

  .min-w-320px {
    min-width: calc(100% - 0.5rem) !important;
  }

  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr)) !important;
  }

  .text-sm {
    font-size: 0.75rem !important;
  }

  button {
    padding: 0.25rem 0.5rem !important;
  }
}