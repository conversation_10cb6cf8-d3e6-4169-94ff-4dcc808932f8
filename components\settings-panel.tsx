'use client'

import { useState } from 'react'
import { ChevronLeft, ChevronRight, User, CreditCard, Package } from 'lucide-react'
import { PersonalInformation } from '@/components/personal-information'

interface SettingsPanelProps {
  onBack: () => void
}

function SettingsMenuItem({ 
  icon, 
  label, 
  onClick 
}: { 
  icon: React.ReactNode; 
  label: string;
  onClick?: () => void;
}) {
  return (
    <button 
      onClick={onClick}
      className="w-full flex items-center justify-between p-3 rounded-[8px] bg-[#28282880] hover:bg-[#282828] transition-colors"
    >
      <div className="flex items-center gap-3">
        {icon}
        <span className="text-sm">{label}</span>
      </div>
      <ChevronRight className="h-5 w-5 text-[#a8a3ac]" />
    </button>
  );
}

export function SettingsPanel({ onBack }: SettingsPanelProps) {
  const [currentView, setCurrentView] = useState<'main' | 'personal-info' | 'payment' | 'subscription'>('main')
  
  const handleBackToMain = () => setCurrentView('main')
  
  if (currentView === 'personal-info') {
    return <PersonalInformation onBack={handleBackToMain} />
  }
  
  return (
    <div className="w-full flex items-center bg-[#08101e] text-white flex-col">
      <div className="flex w-full justify-between mb-3 sm:mb-4 gap-4">
        <h1 className="text-lg sm:text-xl font-medium">Settings</h1>
        <button 
          onClick={onBack}
          className="flex items-center text-[#bdbac0] hover:text-white transition-colors text-sm sm:text-base"
        >
          <ChevronLeft className="h-4 w-4 sm:h-5 sm:w-5 mr-1" />
          <span>Back</span>
        </button>
      </div>

      <div className="h-px bg-[#313038] w-full mb-4 sm:mb-6"></div>

      <div className="space-y-2 w-full">
        <SettingsMenuItem 
          icon={<User className="h-5 w-5" />} 
          label="Personal Information" 
          onClick={() => setCurrentView('personal-info')}
        />
        <SettingsMenuItem 
          icon={<CreditCard className="h-5 w-5" />} 
          label="Payment Settings" 
          onClick={() => setCurrentView('payment')}
        />
        <SettingsMenuItem 
          icon={<Package className="h-5 w-5" />} 
          label="Subscription" 
          onClick={() => setCurrentView('subscription')}
        />
      </div>
    </div>
  )
}
