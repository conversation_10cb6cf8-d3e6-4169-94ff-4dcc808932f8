"use client"

import { useState, useRef, useEffect } from "react"
import { X, Send } from "lucide-react"
import Image from "next/image"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { format } from "date-fns"
import { ScrollArea } from "@/components/ui/scroll-area"

interface ChatAIModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function ChatAIModal({ isOpen, onClose }: ChatAIModalProps) {
  const [message, setMessage] = useState("")
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const [chatHistory, setChatHistory] = useState<{
    role: "user" | "ai";
    content: string;
    avatar: string;
    timestamp: string;
  }[]>([
    {
      role: "ai",
      content: `Hi there! How can I help you today?`,
      avatar: "/white_horse_icon.svg",
      timestamp: format(new Date(), "h:mm a")
    }
  ])

  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [chatHistory]);

  const handleSendMessage = () => {
    if (!message.trim()) return

    const currentTime = format(new Date(), "h:mm a")

    setChatHistory([
      ...chatHistory,
      {
        role: "user",
        content: message,
        avatar: "/sample-avatar.svg",
        timestamp: currentTime
      }
    ])

    setTimeout(() => {
      setChatHistory(prev => [
        ...prev,
        {
          role: "ai",
          content: "This is a simulated response. In the actual implementation, this would be connected to your AI backend.",
          avatar: "/white_horse_icon.svg",
          timestamp: format(new Date(), "h:mm a")
        }
      ])
    }, 1000)

    setMessage("")
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return isOpen ? (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px] bg-[#08101e] border-none p-0 text-white">
        <div className="flex flex-col h-[80vh]">
          <div className="items-center justify-center p-4 border-b border-[#1a2436]">
            <h2 className="text-xl font-medium text-center">Ask your question</h2>
            <div className="flex flex-row justify-center gap-1 mt-1">
              <h2 className="text-lg text-center text-gray-400">Use credits</h2>
              <Image
                src="/star-first.svg"
                alt="Credit Icon"
                width={24}
                height={24}
              />
              <h2 className="text-lg text-center text-gray-400">to ask questions</h2>
            </div>
          </div>

          <ScrollArea ref={scrollAreaRef} className="flex-1 m-4 rounded-lg bg-[#FFFFFF0F]">
            <style jsx global>{`
              .scrollbar-thumb {
                background: linear-gradient(38.98deg, #19FB9B -34.05%, #8C01FA 77.64%) !important;
                width: 2px !important;
                border-radius: 88px !important;
              }
            `}</style>
            <div className="space-y-4 p-4">
              {chatHistory.map((chat, index) => (
                <div
                  key={index}
                  className={`flex ${chat.role === "user" ? "justify-end" : "justify-start"}`}
                >
                  {chat.role === "user" ? (
                    <div className="flex items-start gap-2 relative justify-end">
                      <div
                        className="max-w-[85%] py-4 px-4 text-black relative"
                        style={{
                          borderTopLeftRadius: "10px",
                          borderTopRightRadius: "10px",
                          borderBottomRightRadius: "0",
                          borderBottomLeftRadius: "10px",
                          gap: "13px",
                          background: "#FFFFFF",
                          position: "relative",
                          width: "fit-content",
                          minWidth: "120px",
                          maxWidth: "85%",
                        }}
                      >
                        <div
                          style={{
                            position: "absolute",
                            top: 0,
                            right: 0,
                            bottom: 0,
                            left: 0,
                            borderRadius: "10px 10px 0 10px",
                            padding: "1.5px",
                            background: "#E0E0E0",
                            WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                            WebkitMaskComposite: "xor",
                            maskComposite: "exclude",
                            pointerEvents: "none",
                          }}
                        />
                        <div className="pr-6 pb-4">
                          {chat.content}
                        </div>
                        <div className="absolute bottom-1 right-2">
                          <Image
                            src={chat.avatar}
                            alt="User Avatar"
                            width={28}
                            height={28}
                            className="rounded-full"
                          />
                        </div>
                        <span className="absolute bottom-1 right-12 text-xs text-gray-400">
                          {chat.timestamp}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-start gap-2 relative">
                      <div
                        className="max-w-[85%] py-4 px-4 text-white relative"
                        style={{
                          borderTopLeftRadius: "10px",
                          borderTopRightRadius: "10px",
                          borderBottomRightRadius: "10px",
                          gap: "13px",
                          background: "linear-gradient(261.81deg, rgba(25, 251, 155, 0.4) -8.01%, rgba(140, 1, 250, 0.4) 100%)",
                          position: "relative",
                          width: "fit-content",
                          minWidth: "120px",
                          maxWidth: "85%",
                        }}
                      >
                        <div
                          style={{
                            position: "absolute",
                            top: 0,
                            right: 0,
                            bottom: 0,
                            left: 0,
                            borderRadius: "10px 10px 10px 0",
                            padding: "1.5px",
                            background: "linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%)",
                            WebkitMask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                            WebkitMaskComposite: "xor",
                            maskComposite: "exclude",
                            pointerEvents: "none",
                          }}
                        />
                        <div className="flex items-center gap-2">
                          <div className="flex-shrink-0">
                            <Image
                              src={chat.avatar}
                              alt="AI Avatar"
                              width={28}
                              height={28}
                            />
                          </div>
                          <div className="pt-1">
                            {chat.content}
                          </div>
                        </div>
                        <span className="absolute top-1 right-2 text-xs text-gray-400">
                          {chat.timestamp}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>

          <div className="p-4 border-t border-[#1a2436]">
            <Input
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your message..."
              className="bg-[#FFFFFF0F] text-white border-none rounded-[8px]"
            />
            <div className="flex justify-between gap-2 p-2">
              <div className="flex flex-row gap-2">
                <Image
                  src="/star-first.svg"
                  alt="Credit Icon"
                  width={24}
                  height={24}
                />
                <p className="text-md text-white mt-2">10 Chat</p>
              </div>
              <Button
                onClick={handleSendMessage}
                className="bg-[#7610DB] hover:bg-[#7610DB]/90 text-white rounded-full"
              >
                Buy Chat Points
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  ) : null
}







