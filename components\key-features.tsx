import Vector1Icon from "./svg/sand-icon"
import DiagramIcon from "./svg/diagram-icon"
import JudgeIcon from "./svg/judge-icon"
import StarIcon from "./svg/star-icon"
import MessageIcon from "./svg/message-icon"

export default function KeyFeatures() {
  return (
    <section id="features" className="py-16 md:py-24 relative">
      <div className="container mx-auto text-center">
        <h2 className="text-[28px] md:text-5xl font-bold mb-16">
          <span className="bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] bg-clip-text text-transparent">
            Key Features & Benefits
          </span>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-[10px] max-w-6xl mx-auto mb-[10px]">
          <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-[200px] p-6 text-left mx-auto">
            <div className="mb-4">
              <Vector1Icon className="h-8 w-8" />
            </div>
            <h3 className="text-white text-[20px] font-medium mb-2">Real-Time Pre-Game Predictions</h3>
            <p className="text-white/70 text-[14px]">Updated as new team statistics become available.</p>
          </div>

          <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-[200px] p-6 text-left mx-auto">
            <div className="mb-4">
              <DiagramIcon className="h-8 w-8" />
            </div>
            <h3 className="text-white text-[20px] font-medium mb-2">Deep Statistical Analysis</h3>
            <p className="text-white/70 text-[14px]">
              Trained on historical player performance, injuries, and matchup dynamics.
            </p>
          </div>

          <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-[200px] p-6 text-left mx-auto">
            <div className="mb-4">
              <JudgeIcon className="h-8 w-8" />
            </div>
            <h3 className="text-white text-[20px] font-medium mb-2">No Bookmaker Bias</h3>
            <p className="text-white/70 text-[14px]">Independent AI sports predictions with no agenda.</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-[10px] max-w-[810px] mx-auto mb-12">
          <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-[200px] p-6 text-left mx-auto">
            <div className="mb-4">
              <StarIcon className="h-8 w-8" />
            </div>
            <h3 className="text-white text-[20px] font-medium mb-2">Confidence Ratings</h3>
            <p className="text-white/70 text-[14px]">Know exactly how confident our AI is in each prediction.</p>
          </div>

          <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-[200px] p-6 text-left mx-auto">
            <div className="mb-4">
              <MessageIcon className="h-8 w-8" />
            </div>
            <h3 className="text-white text-[20px] font-medium mb-2">Integrated AI Bot</h3>
            <p className="text-white/70 text-[14px]">Ask questions about any matchup or prediction.</p>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 w-full">
          <button className="bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white text-[16px] sm:text-[14px] font-medium rounded-full w-full sm:w-[224px] h-[56px] sm:h-[48px] flex items-center justify-center hover:opacity-90 transition-opacity">
            Get AI Predictions
          </button>
          <button className="border border-white/20 bg-transparent text-white text-[16px] sm:text-[14px] font-medium rounded-full w-full sm:w-[224px] h-[56px] sm:h-[48px] flex items-center justify-center hover:bg-white/10 transition-all">
            Sign Up for Exclusive Access
          </button>
        </div>
      </div>
    </section>
  )
}





